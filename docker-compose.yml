version: '3.8'

services:
  mongo:
    image: mongo:6.0
    container_name: mycase_mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    environment:
      MONGO_INITDB_DATABASE: mycase

  backend:
    build: ./backend
    container_name: mycase_backend
    restart: unless-stopped
    ports:
      - "5001:5000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/mycase
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - FRONTEND_URL=http://localhost:3001
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
    depends_on:
      - mongo

  frontend:
    build: ./frontend
    container_name: mycase_frontend
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5001/api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    stdin_open: true
    tty: true

volumes:
  mongo_data: