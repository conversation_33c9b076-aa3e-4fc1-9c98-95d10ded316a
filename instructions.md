Another situation is that there are cases of incomplete data. For example, is the data with only names in laywers.csv considered valid data? Do you need to write it to the database?
Those must be from attys who no longer work at the firm. You could mock the corresponding atty records
Python is fine for scripts but not the ui
The UI needs to reflect some of the major tables that were missing especially the financial tracking, balances, customer contact information, and calendar.
Once you have the UI properly reflecting the information in the tables from those sources we'll look at it and see what we should do next.
At present, some of the optimization has been made, and the addition, deletion and modification interfaces need to be continuously improved. During the week, because I have to go to work during the day, the time may not be guaranteed.
Try to extract the spare time from work within the week to accelerate the completion.

1. Mycase lead dashboard looks like this
![img.png](img.png)

2. Case management page should look like this (you can click each case and it takes you to the next page showing case decription, calendar, invoice, payment, notes etc - see below)
![img_1.png](img_1.png)


3. Task page should look like this where you can choose assigned to "all firm user, attorney, paralegal" to see a specic staff's tasks (past task, today task and future tasks in a timeline). Each task is linked to each case
![img_2.png](img_2.png)

4. Calendar page should look like this (each calender event linked to a case, eash staff has a color, each even shows which staff will attend, even types)
![img_3.png](img_3.png)


5. Under each case, you click billing, it looks like this (contrave value, attorney fee, recorded payment, remaining balance, retainer, time entries, expenses etc...)
![img_4.png](img_4.png)
