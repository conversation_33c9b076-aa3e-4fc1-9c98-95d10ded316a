# 🏛️ 榴莲律师事务所管理系统 - 需求分析文档

## 📋 业务背景与目标

**项目目标**：开发一个完整的律师事务所管理系统，替代现有的 MyCase 系统，提供从线索获取到案件结案收款的全流程数字化管理。

**核心价值**：
- 统一管理律所所有业务流程
- 提高工作效率和客户满意度
- 实现精确的财务跟踪和账单管理
- 支持多角色协作和任务分配

## 👥 用户角色与权限

| 角色 | 职责 | 主要功能 |
|------|------|----------|
| **管理员 (Admin)** | 律所运营管理 | 系统配置、用户管理、财务报表、数据导入 |
| **律师 (Attorney)** | 案件主要负责人 | 案件管理、客户沟通、法律文档、时间记录 |
| **案件经理 (Case Manager)** | 案件协调管理 | 任务分配、进度跟踪、客户联系、文档整理 |
| **法务助理 (Paralegal)** | 协助律师工作 | 文档准备、研究工作、客户沟通、数据录入 |
| **秘书 (Secretary)** | 行政支持 | 日程安排、文档管理、电话接听、数据录入 |

## 🔄 核心业务流程

```
潜在客户咨询 → 创建线索 → 冲突检查 → 转换为案件 → 签署合同 → 收取预付款 
→ 分配律师团队 → 创建任务计划 → 执行法律服务 → 记录工时费用 → 生成账单 
→ 客户付款 → 案件结案 → 最终结算 → 归档文档
```

## 📊 功能模块详细说明

### 1. 线索管理 (Leads Management)
**用户故事**：
> 作为一名接待员，我需要快速记录潜在客户的咨询信息，包括联系方式、案件类型和初步需求，以便律师能够及时跟进。

**核心功能**：
- 线索信息录入（姓名、联系方式、案件类型、来源）
- 冲突检查记录
- 线索状态跟踪（新建、已联系、已确认、已转换、已关闭）
- 跟进提醒和任务分配
- 转换为正式案件

### 2. 案件管理 (Case Management)
**用户故事**：
> 作为一名律师，我需要一个集中的地方查看我负责的所有案件信息，包括客户详情、案件进展、重要日期和财务状况，以便我能够有效管理我的工作负载。

**核心功能**：
- 案件基本信息管理（案件号、客户信息、案件类型、状态）
- 案件详情页面整合：
  - 案件描述和背景
  - 重要日期和截止日期
  - 相关文档和证据
  - 案件笔记和沟通记录
  - 财务信息（账单、付款、余额）
  - 任务和日程安排

### 3. 任务管理 (Task Management)
**用户故事**：
> 作为一名案件经理，我需要为团队成员分配具体任务，设置截止日期和优先级，并能够跟踪任务完成情况，确保案件按时推进。

**核心功能**：
- 任务创建和分配
- 优先级设置（低、普通、高、紧急）
- 截止日期管理
- 任务状态跟踪（待处理、进行中、已完成、已取消）
- **时间线视图**（按员工筛选：所有员工、律师、法务助理）
- 子任务管理
- 任务关联案件

### 4. 时间记录 (Time Tracking)
**用户故事**：
> 作为一名律师，我需要准确记录为每个案件投入的时间，包括活动类型、时长和详细描述，以便准确计费并分析工作效率。

**核心功能**：
- 工时记录（日期、活动、时长、描述）
- 计费费率管理
- 可计费/不可计费时间区分
- 时间汇总和报表
- 与案件和发票关联

### 5. 财务管理 (Financial Management)
**用户故事**：
> 作为律所管理员，我需要清楚地了解每个案件的财务状况，包括已计费金额、已收款项、未结余额和信托账户余额，以便做出正确的财务决策。

**核心功能**：
- **详细的账单页面**显示：
  - 合同价值 (Contract Value)
  - 律师费 (Attorney Fees)
  - 已记录支付 (Recorded Payments)
  - 剩余余额 (Remaining Balance)
  - 预付款/信托余额 (Retainer/Trust Balance)
  - 时间条目汇总 (Time Entries)
  - 费用明细 (Expenses)
- 发票生成和管理
- 支付记录和处理
- 信托账户管理
- 财务报表生成

### 6. 日历管理 (Calendar Management)
**用户故事**：
> 作为律所员工，我需要一个统一的日历视图，显示所有与案件相关的事件，每个员工用不同颜色标识，显示参与人员和事件类型，帮助我合理安排时间。

**核心功能**：
- **增强的日历页面**：
  - 每个日历事件关联具体案件
  - 每个员工有独特的颜色标识
  - 显示事件参与的员工
  - 事件类型分类（庭审、会议、截止日期等）
- 事件创建和编辑
- 提醒和通知
- 日程冲突检测

## 🚧 待完成功能需求（基于 instructions.md）

### 高优先级改进：

1. **财务跟踪优化**
   - 完善财务仪表板
   - 增强余额管理和显示
   - 改进收支报表功能

2. **客户联系信息管理**
   - 优化联系人信息展示
   - 增强客户沟通记录
   - 改进联系人搜索和筛选

3. **UI界面优化**（参考MyCase设计）
   - **Lead Dashboard** - 线索仪表板界面优化
   - **Case Management** - 案件管理页面，点击案件查看完整详情
   - **Task Timeline** - 任务时间线视图，支持按员工类型筛选
   - **Enhanced Calendar** - 增强日历功能，员工颜色标识和事件详情
   - **Detailed Billing** - 详细账单页面，显示完整财务信息

4. **数据完整性处理**
   - 处理不完整的律师数据
   - 为离职律师创建模拟记录
   - 数据清理和验证

### 技术改进：

1. **API接口持续优化**
   - 完善增删改查接口
   - 提高接口性能和稳定性
   - 增强错误处理

2. **用户体验优化**
   - 响应式设计改进
   - 加载性能优化
   - 交互体验提升

## 🛠️ 技术架构

**后端技术栈**：
- Node.js + Express.js
- MongoDB + Mongoose
- JWT 身份认证
- Multer 文件上传
- Square API 支付集成

**前端技术栈**：
- React.js
- Material-UI 组件库
- React Router 路由管理
- Axios HTTP 客户端

**数据迁移**：
- Python 脚本从 MyCase 系统导入历史数据
- CSV 文件处理和数据清理

## 📈 项目当前状态

✅ **已完成**：
- 基础的 CRUD 功能
- 用户认证和角色管理
- 核心业务模块框架
- 数据模型设计
- 基础 UI 界面

🔄 **进行中**：
- UI 界面优化和完善
- API 接口改进
- 数据迁移和清理

⏳ **待开始**：
- 高级财务报表功能
- 移动端适配
- 系统集成测试

## 📝 关键用户故事汇总

### 管理员视角
- 我需要查看律所整体运营数据，包括收入、案件数量、员工效率等关键指标
- 我需要管理用户账户和权限，确保数据安全和访问控制
- 我需要生成各种财务和业务报表，支持决策制定

### 律师视角
- 我需要快速查看我负责的所有案件状态和重要截止日期
- 我需要记录与客户的沟通和案件进展，保持完整的案件历史
- 我需要准确记录工作时间，确保计费的准确性

### 案件经理视角
- 我需要协调团队成员的工作，分配任务并跟踪进度
- 我需要与客户保持沟通，及时更新案件状态
- 我需要管理案件相关的所有文档和证据

### 法务助理视角
- 我需要协助律师完成各种法律文档的准备工作
- 我需要进行法律研究并整理相关资料
- 我需要与客户和法院进行日常沟通

## 🎯 成功标准

1. **功能完整性**：所有核心业务流程都能在系统中完成
2. **用户体验**：界面友好，操作简便，响应迅速
3. **数据准确性**：财务数据和案件信息准确无误
4. **系统稳定性**：7x24小时稳定运行，数据安全可靠
5. **业务效率**：相比原有流程，工作效率提升30%以上

---

*本文档基于对项目代码的深入分析和 instructions.md 的需求整理而成，为项目开发和优化提供指导。*
