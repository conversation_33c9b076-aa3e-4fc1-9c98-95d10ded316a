#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Fixed MyCase MongoDB Importer
-----------------------------
This script imports MyCase data exported as CSV files into a MongoDB database.
It handles various data types including cases, contacts, companies, lawyers,
tasks, documents, time entries, expenses, invoices, notes, and more.

Features:
- Robust date parsing with multiple format support
- Duplicate prevention using unique identifiers
- Comprehensive logging
- Detailed import summary
- Error handling and recovery
"""

import os
import sys
import pandas as pd
import pymongo
from datetime import datetime
import logging
from typing import Dict, Any, List
import json
from pymongo import UpdateOne 

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MyCaseMongoImporter:
    def __init__(self, csv_directory: str, mongo_uri: str = "mongodb://localhost:27017/", db_name: str = "mycase"):
        """
        Initialize the importer
        
        Args:
            csv_directory: Directory containing CSV files
            mongo_uri: MongoDB connection URI
            db_name: Database name
        """
        self.csv_directory = csv_directory
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.client = None
        self.db = None
        
    def connect_mongo(self) -> bool:
        """Connect to MongoDB"""
        try:
            self.client = pymongo.MongoClient(self.mongo_uri)
            self.db = self.client[self.db_name]
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"Successfully connected to MongoDB: {self.mongo_uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
    
    def close_mongo(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean data"""
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Replace NaN with empty string
        df = df.fillna('')
        
        # Clean string fields
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].astype(str).str.strip()
        
        return df
    
    def parse_date(self, date_str: str) -> datetime:
        """Parse date string"""
        if not date_str or date_str == '' or date_str == 'nan':
            return None
        
        try:
            # Try multiple date formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%m/%d/%Y %H:%M:%S',
                '%d/%m/%Y',
                '%d/%m/%Y %H:%M:%S',
                '%m/%d/%y',  # 2-digit year format
                '%d/%m/%y',  # 2-digit year format
                '%Y-%m-%d %H:%M:%S%z',  # With timezone
                '%Y-%m-%d %H:%M:%S %z',  # With timezone
                '%m/%d/%Y %H:%M:%S %z',  # With timezone
                '%d/%m/%Y %H:%M:%S %z',  # With timezone
                '%Y-%m-%dT%H:%M:%S',  # ISO format
                '%Y-%m-%dT%H:%M:%S.%f',  # ISO format with milliseconds
                '%Y-%m-%dT%H:%M:%S%z',  # ISO format with timezone
                '%Y-%m-%dT%H:%M:%S.%f%z',  # ISO format with milliseconds and timezone
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(str(date_str), fmt)
                except ValueError:
                    continue
            
            # If standard formats fail, try more flexible parsing
            try:
                # Handle timezone information in date string
                if ' -' in str(date_str) or ' +' in str(date_str):
                    # Remove timezone info, keep only date part
                    date_part = str(date_str).split(' -')[0].split(' +')[0]
                    return datetime.strptime(date_part, '%Y-%m-%d %H:%M:%S')
            except:
                pass
            
            logger.warning(f"Unable to parse date: {date_str}")
            return None
        except Exception as e:
            logger.warning(f"Date parsing error: {date_str}, {e}")
            return None
    
    def parse_number(self, value: str) -> float:
        """Parse number"""
        if not value or value == '' or value == 'nan':
            return 0.0
        
        try:
            # Remove currency symbols and commas
            cleaned = str(value).replace('$', '').replace(',', '').strip()
            return float(cleaned)
        except:
            return 0.0
    
    def safe_get(self, row, field_name: str, default: str = '') -> str:
        """Safely get field value"""
        try:
            value = row.get(field_name, default)
            if pd.isna(value):
                return default
            return str(value).strip()
        except:
            return default
    
    def import_cases(self):
        """Import case data - fixed field mapping"""
        try:
            file_path = os.path.join(self.csv_directory, 'cases.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting case data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.cases
            imported_count = 0
            cnt = 0
            for _, row in df.iterrows():
                case_data = {
                    'caseNumber': self.safe_get(row, 'Number', ''),
                    'caseName': self.safe_get(row, 'Case/Matter Name', ''),
                    'clientName': self.safe_get(row, 'Client Name', ''),
                    'clientEmail': self.safe_get(row, 'E-mail Address', ''),  # Add client email
                    'clientPhone': self.safe_get(row, 'Phone No.', ''),
                    'caseType': self.safe_get(row, 'Practice Area', 'other'),  # Map practice area to case type
                    'practiceArea': self.safe_get(row, 'Practice Area', ''),
                    'openDate': self.parse_date(self.safe_get(row, 'Open Date', '')),
                    'closedDate': self.parse_date(self.safe_get(row, 'Closed Date', '')),
                    'solDate': self.parse_date(self.safe_get(row, 'SOL Date', '')),
                    'referralSource': self.safe_get(row, 'Where clients come from', ''),
                    'leadAttorney': self.safe_get(row, 'Lead Attorney Name', ''),
                    'originatingAttorney': self.safe_get(row, 'Originating Attorney Name', ''),
                    'billingType': self.safe_get(row, 'Billing Type', ''),
                    'outstandingBalance': self.parse_number(self.safe_get(row, 'Outstanding Balance', '0')),
                    'caseBalance': self.parse_number(self.safe_get(row, 'Case Balance', '0')),
                    'retainer': self.parse_number(self.safe_get(row, 'Retainer', '0')),
                    'retainerDate': self.parse_date(self.safe_get(row, 'Date of K', '')),
                    'status': 'closed' if self.safe_get(row, 'Closed', '').lower() == 'yes' else 'open',
                    'description': self.safe_get(row, 'Case Description', ''),
                    'importantDeadlines': self.safe_get(row, 'IMPORTANT DEADLINES', ''),
                    'deadlineDescription': self.safe_get(row, 'DEADLINE DESCRIPTION', ''),
                    'caseStage': self.safe_get(row, 'Case Stage', ''),
                    'flatFee': self.safe_get(row, 'Flat fee', ''),
                    'billingContact': self.safe_get(row, 'Billing Contact', ''),
                    'office': self.safe_get(row, 'Office', ''),
                    'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                    'contacts': self.safe_get(row, 'Contacts', ''),
                    'createdBy': self.safe_get(row, 'Created By', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                # Use caseNumber as unique identifier
                if case_data['caseNumber']:
                    collection.update_one(
                        {'caseNumber': case_data['caseNumber']},
                        {'$set': case_data},
                        upsert=True
                    )
                    imported_count += 1
            logger.info(f"Successfully imported {imported_count} case records")
            
        except Exception as e:
            logger.error(f"Failed to import case data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_people(self):
        """Import contact data - optimized version, import all records"""
        try:
            file_path = os.path.join(self.csv_directory, 'people.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting contact data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            email_count = df['E-mail Address'].notna().sum() if 'E-mail Address' in df.columns else 0
            mycase_id_count = df['MyCase ID'].notna().sum() if 'MyCase ID' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Records with email address: {email_count}")
            logger.info(f"Records with MyCase ID: {mycase_id_count}")
            
            collection = self.db.contacts
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    contact_data = {
                        'firstName': self.safe_get(row, 'First Name', ''),
                        'middleName': self.safe_get(row, 'Middle Name', ''),
                        'lastName': self.safe_get(row, 'Last Name', ''),
                        'email': self.safe_get(row, 'E-mail Address', ''),
                        'phone': self.safe_get(row, 'Home Phone', ''),
                        'mobile': self.safe_get(row, 'Mobile Phone', ''),
                        'workPhone': self.safe_get(row, 'Work Phone', ''),
                        'address': {
                            'street': self.safe_get(row, 'Home Street', ''),
                            'street2': self.safe_get(row, 'Home Street 2', ''),
                            'city': self.safe_get(row, 'Home City', ''),
                            'state': self.safe_get(row, 'Home State', ''),
                            'postalCode': self.safe_get(row, 'Home Postal Code', ''),
                            'country': self.safe_get(row, 'Home Country/Region', '')
                        },
                        'company': self.safe_get(row, 'Company', ''),
                        'title': self.safe_get(row, 'Job Title', ''),
                        'website': self.safe_get(row, 'Web Page', ''),
                        'trustBalance': self.parse_number(self.safe_get(row, 'Outstanding Trust Balance', '0')),
                        'creditBalance': self.parse_number(self.safe_get(row, 'Non-Trust Credit Balance', '0')),
                        'archived': self.safe_get(row, 'Archived', '').lower() == 'true',
                        'loginEnabled': self.safe_get(row, 'Login Enabled', '').lower() == 'true',
                        'birthday': self.parse_date(self.safe_get(row, 'Birthday', '')),
                        'licenseNumber': self.safe_get(row, 'License Number', ''),
                        'licenseState': self.safe_get(row, 'License State', ''),
                        'notes': self.safe_get(row, 'Private Notes', ''),
                        'cases': self.safe_get(row, 'Cases', ''),
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'createdDate': self.parse_date(self.safe_get(row, 'Created Date', '')),
                        'createdBy': self.safe_get(row, 'Created By', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if contact_data['myCaseId']:
                        unique_id = f"mycase_{contact_data['myCaseId']}"
                    elif contact_data['email']:
                        unique_id = f"email_{contact_data['email']}"
                    else:
                        # Use name combination
                        name_parts = [contact_data['firstName'], contact_data['middleName'], contact_data['lastName']]
                        name_id = '_'.join([part for part in name_parts if part])
                        if name_id:
                            unique_id = f"name_{name_id}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**contact_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} contact records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import contact data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_companies(self):
        """Import company data - fixed field mapping"""
        try:
            file_path = os.path.join(self.csv_directory, 'companies.csv')
            if not os.path.exists(file_path):
                logger.warning("Company data file does not exist")
                return
            
            logger.info("Starting company data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            collection = self.db.companies
            imported_count = 0
            
            for _, row in df.iterrows():
                company_data = {
                    'name': self.safe_get(row, 'Company', ''),
                    'address': {
                        'street': self.safe_get(row, 'Business Street', ''),
                        'street2': self.safe_get(row, 'Business Street 2', ''),
                        'city': self.safe_get(row, 'Business City', ''),
                        'state': self.safe_get(row, 'Business State', ''),
                        'postalCode': self.safe_get(row, 'Business Postal Code', ''),
                        'country': self.safe_get(row, 'Business Country/Region', '')
                    },
                    'phone': self.safe_get(row, 'Company Main Phone', ''),
                    'fax': self.safe_get(row, 'Business Fax', ''),
                    'email': self.safe_get(row, 'E-mail Address', ''),
                    'website': self.safe_get(row, 'Web Page', ''),
                    'trustBalance': self.parse_number(self.safe_get(row, 'Outstanding Trust Balance', '0')),
                    'creditBalance': self.parse_number(self.safe_get(row, 'Non-Trust Credit Balance', '0')),
                    'archived': self.safe_get(row, 'Archived', '').lower() == 'yes',
                    'notes': self.safe_get(row, 'Private Notes', ''),
                    'contacts': self.safe_get(row, 'Contacts', ''),
                    'cases': self.safe_get(row, 'Cases', ''),
                    'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                    'createdDate': self.parse_date(self.safe_get(row, 'Created Date', '')),
                    'createdBy': self.safe_get(row, 'Created By', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                if company_data['name']:
                    collection.update_one(
                        {'name': company_data['name']},
                        {'$set': company_data},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} company records")
            
        except Exception as e:
            logger.error(f"Failed to import company data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_lawyers(self):
        """Import lawyer data """
        try:
            file_path = os.path.join(self.csv_directory, 'lawyers.csv')
            if not os.path.exists(file_path):
                logger.warning("Lawyer data file does not exist")
                return
            
            logger.info("Starting lawyer data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            collection = self.db.lawyers
            imported_count = 0
            now = datetime.now()
            operations = []
            cnt = 1
            for _, row in df.iterrows():
                print(cnt)
                cnt = cnt + 1
                lawyer_data = {
                    'firstName': self.safe_get(row, 'First name', ''),
                    'middleName': self.safe_get(row, 'Middle Name', ''),
                    'lastName': self.safe_get(row, 'Last name', ''),
                    'email': self.safe_get(row, 'Email', ''),
                    'userType': self.safe_get(row, 'User Type', ''),
                    'address': {
                        'street': self.safe_get(row, 'Street', ''),
                        'street2': self.safe_get(row, 'Street 2', ''),
                        'city': self.safe_get(row, 'City', ''),
                        'state': self.safe_get(row, 'State', ''),
                        'postalCode': self.safe_get(row, 'Postal Code', ''),
                        'country': self.safe_get(row, 'Country/Region', '')
                    },
                    'phone': {
                        'home': self.safe_get(row, 'Home phone', ''),
                        'cell': self.safe_get(row, 'Cell phone', ''),
                        'work': self.safe_get(row, 'Work phone', ''),
                        'fax': self.safe_get(row, 'Fax phone', '')
                    },
                    'defaultRate': self.parse_number(self.safe_get(row, 'Default rate', '0')),
                    'archived': self.safe_get(row, 'Archived', '').lower() == 'yes',
                    'cases': self.safe_get(row, 'Cases', ''),
                    'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                    'createdAt': now,
                    'updatedAt': now
                }

                # 定义唯一键（优先 email，没有则用姓名组合）
                if lawyer_data['email']:
                    unique_filter = {'email': lawyer_data['email']}
                else:
                    unique_filter = {
                        'firstName': lawyer_data['firstName'],
                        'lastName': lawyer_data['lastName']
                    }

                operations.append(
                    UpdateOne(unique_filter, {'$set': lawyer_data}, upsert=True)
                )
                imported_count += 1

            if operations:
                collection.bulk_write(operations, ordered=False)

            logger.info(f"Successfully imported {imported_count} lawyer records")

        except Exception as e:
            logger.error(f"Failed to import lawyer data: {e}")
            import traceback
            traceback.print_exc()

    
    def import_tasks(self):
        """Import task data"""
        try:
            file_path = os.path.join(self.csv_directory, 'tasks.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting task data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.tasks
            imported_count = 0
            
            for _, row in df.iterrows():
                task_data = {
                    'title': self.safe_get(row, 'Task Name', ''),
                    'description': self.safe_get(row, 'Description', ''),
                    'caseName': self.safe_get(row, 'Case Name', ''),
                    'assignedTo': self.safe_get(row, 'Assigned To', ''),
                    'assignedBy': self.safe_get(row, 'Assigned By', ''),
                    'priority': self.safe_get(row, 'Priority', 'medium'),
                    'status': self.safe_get(row, 'Status', 'pending'),
                    'dueDate': self.parse_date(self.safe_get(row, 'Due Date', '')),
                    'completedDate': self.parse_date(self.safe_get(row, 'Completed At', '')),
                    'completedBy': self.safe_get(row, 'Completed By', ''),
                    'totalEstimate': self.parse_number(self.safe_get(row, 'Total Task Estimate', '0')),
                    'subtasks': self.safe_get(row, 'Subtasks', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use task title and case name as unique identifier
                unique_id = f"{task_data['title']}_{task_data['caseName']}"
                if task_data['title']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**task_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} task records")
            
        except Exception as e:
            logger.error(f"Failed to import task data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_documents(self):
        """Import document data - fixed field mapping"""
        try:
            file_path = os.path.join(self.csv_directory, 'documents.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting document data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            logger.info(f"CSV file contains {total_rows} rows")
            # logger.info(f"CSV columns: {list(df.columns)}")
            
            collection = self.db.documents
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    document_data = {
                        'name': self.safe_get(row, 'Name', ''),
                        'description': self.safe_get(row, 'Description', ''),
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'archived': self.safe_get(row, 'Archived', '').lower() == 'true',
                        'template': self.safe_get(row, 'Template', '').lower() == 'true',
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'tags': self.safe_get(row, 'Tags', ''),
                        'versions': self.safe_get(row, 'Versions', ''),
                        'comments': self.safe_get(row, 'Comments', ''),
                        'sharedWith': self.safe_get(row, 'Shared With', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if document_data['myCaseId']:
                        unique_id = f"mycase_{document_data['myCaseId']}"
                    elif document_data['name'] and document_data['caseName']:
                        unique_id = f"{document_data['name']}_{document_data['caseName']}"
                    elif document_data['name']:
                        unique_id = f"name_{document_data['name']}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**document_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} document records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import document data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_time_entries(self):
        """Import time entry data"""
        try:
            file_path = os.path.join(self.csv_directory, 'time_entries.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting time entry data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.timeEntries
            imported_count = 0
            
            for _, row in df.iterrows():
                time_entry_data = {
                    'date': self.parse_date(self.safe_get(row, 'Date', '')),
                    'caseName': self.safe_get(row, 'Case Name', ''),
                    'description': self.safe_get(row, 'Description', ''),
                    'hours': self.parse_number(self.safe_get(row, 'Hours', '0')),
                    'rate': self.parse_number(self.safe_get(row, 'Rate', '0')),
                    'amount': self.parse_number(self.safe_get(row, 'Amount', '0')),
                    'attorney': self.safe_get(row, 'Attorney', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use date, case name and description as unique identifier
                unique_id = f"{time_entry_data['date']}_{time_entry_data['caseName']}_{time_entry_data['description']}"
                if time_entry_data['date'] and time_entry_data['caseName']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**time_entry_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} time entries")
            
        except Exception as e:
            logger.error(f"Failed to import time entry data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_expenses(self):
        """Import expense data"""
        try:
            file_path = os.path.join(self.csv_directory, 'expenses.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting expense data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.expenses
            imported_count = 0
            
            for _, row in df.iterrows():
                expense_data = {
                    'date': self.parse_date(self.safe_get(row, 'Date', '')),
                    'caseName': self.safe_get(row, 'Case Name', ''),
                    'description': self.safe_get(row, 'Description', ''),
                    'amount': self.parse_number(self.safe_get(row, 'Amount', '0')),
                    'category': self.safe_get(row, 'Category', ''),
                    'attorney': self.safe_get(row, 'Attorney', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use date, case name and description as unique identifier
                unique_id = f"{expense_data['date']}_{expense_data['caseName']}_{expense_data['description']}"
                if expense_data['date'] and expense_data['caseName']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**expense_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} expense records")
            
        except Exception as e:
            logger.error(f"Failed to import expense data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_invoices(self):
        """Import invoice data"""
        try:
            file_path = os.path.join(self.csv_directory, 'invoices.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting invoice data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.invoices
            imported_count = 0
            
            for _, row in df.iterrows():
                invoice_data = {
                    'invoiceNumber': self.safe_get(row, 'Invoice Number', ''),
                    'caseName': self.safe_get(row, 'Case Name', ''),
                    'issueDate': self.parse_date(self.safe_get(row, 'Invoice date', '')),
                    'dueDate': self.parse_date(self.safe_get(row, 'Due date', '')),
                    'totalAmount': self.parse_number(self.safe_get(row, 'Total amount', '0')),
                    'status': self.safe_get(row, 'Status', ''),
                    'clientName': self.safe_get(row, 'Client Name', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                if invoice_data['invoiceNumber']:
                    collection.update_one(
                        {'invoiceNumber': invoice_data['invoiceNumber']},
                        {'$set': invoice_data},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} invoice records")
            
        except Exception as e:
            logger.error(f"Failed to import invoice data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_leads(self):
        """Import lead data - fixed field mapping"""
        try:
            file_path = os.path.join(self.csv_directory, 'leads.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting lead data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            logger.info(f"CSV file contains {total_rows} rows")
            # logger.info(f"CSV columns: {list(df.columns)}")
            
            collection = self.db.leads
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    # Build full name
                    first_name = self.safe_get(row, 'First Name', '')
                    middle_name = self.safe_get(row, 'Middle Name', '')
                    last_name = self.safe_get(row, 'Last Name', '')
                    full_name = ' '.join([part for part in [first_name, middle_name, last_name] if part])
                    
                    lead_data = {
                        'firstName': first_name,
                        'middleName': middle_name,
                        'lastName': last_name,
                        'fullName': full_name,
                        'email': self.safe_get(row, 'Lead Contact Email', ''),
                        'phone': self.safe_get(row, 'Lead Contact Cell #', ''),
                        'workPhone': self.safe_get(row, 'Lead Contact Work #', ''),
                        'homePhone': self.safe_get(row, 'Lead Contact Home #', ''),
                        'address': {
                            'street': self.safe_get(row, 'Street', ''),
                            'street2': self.safe_get(row, 'Street 2', ''),
                            'city': self.safe_get(row, 'City', ''),
                            'state': self.safe_get(row, 'State', ''),
                            'zipCode': self.safe_get(row, 'Zip Code', ''),
                            'country': self.safe_get(row, 'Country', '')
                        },
                        'status': self.safe_get(row, 'Current Status', ''),
                        'daysSinceAdded': self.parse_number(self.safe_get(row, 'Days since added', '0')),
                        'convertedDate': self.parse_date(self.safe_get(row, 'Converted Date', '')),
                        'practiceArea': self.safe_get(row, 'Practice Area', ''),
                        'office': self.safe_get(row, 'Office', ''),
                        'details': self.safe_get(row, 'Details', ''),
                        'value': self.parse_number(self.safe_get(row, 'Value', '0')),
                        'source': self.safe_get(row, 'Lead Source', ''),
                        'referredBy': self.safe_get(row, 'Lead Referred By', ''),
                        'birthday': self.parse_date(self.safe_get(row, 'Birthday', '')),
                        'licenseNumber': self.safe_get(row, 'License Number', ''),
                        'licenseState': self.safe_get(row, 'License State', ''),
                        'createdBy': self.safe_get(row, 'Created By', ''),
                        'notes': self.safe_get(row, 'Notes', ''),
                        'addedDate': self.parse_date(self.safe_get(row, 'Added Date', '')),
                        'assignedTo': self.safe_get(row, 'Assign To', ''),
                        'potentialCaseName': self.safe_get(row, 'Potential Case Name', ''),
                        'potentialCaseDescription': self.safe_get(row, 'Potential Case Description', ''),
                        'conflictCheck': self.safe_get(row, 'Conflict Check?', '').lower() == 'true',
                        'conflictCheckNotes': self.safe_get(row, 'Conflict Check Notes', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize name and email combination
                    unique_id = None
                    if full_name and lead_data['email']:
                        unique_id = f"{full_name}_{lead_data['email']}"
                    elif full_name:
                        unique_id = f"name_{full_name}"
                    elif lead_data['email']:
                        unique_id = f"email_{lead_data['email']}"
                    else:
                        # Use index as last resort
                        unique_id = f"index_{index}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**lead_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} lead records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import lead data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_notes(self):
        """Import note data - optimized version to match actual CSV column names"""
        try:
            file_path = os.path.join(self.csv_directory, 'notes.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting note data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            logger.info(f"CSV file contains {total_rows} rows")
            
            # Check column names
            # logger.info(f"CSV columns: {list(df.columns)}")
            
            collection = self.db.notes
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    note_data = {
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'createdBy': self.safe_get(row, 'Created By', ''),
                        'date': self.parse_date(self.safe_get(row, 'Date', '')),
                        'createdAt': self.parse_date(self.safe_get(row, 'Created at', '')),
                        'updatedAt': self.parse_date(self.safe_get(row, 'Updated at', '')),
                        'subject': self.safe_get(row, 'Subject', ''),
                        'note': self.safe_get(row, 'Note', ''),
                        'importedAt': datetime.now()
                    }
                    
                    # Create unique identifier - use case name, creator and subject
                    unique_id = None
                    if note_data['caseName'] and note_data['createdBy'] and note_data['subject']:
                        unique_id = f"{note_data['caseName']}_{note_data['createdBy']}_{note_data['subject']}"
                    elif note_data['caseName'] and note_data['createdBy']:
                        unique_id = f"{note_data['caseName']}_{note_data['createdBy']}_{index}"
                    elif note_data['caseName']:
                        unique_id = f"{note_data['caseName']}_{index}"
                    else:
                        unique_id = f"note_{index}"
                    
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**note_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} note records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import note data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_trust_activities(self):
        """Import trust activity data - optimized version to match actual CSV column names"""
        try:
            file_path = os.path.join(self.csv_directory, 'trust_activities.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting trust activity data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            logger.info(f"CSV file contains {total_rows} rows")
            
            # Check column names
            # logger.info(f"CSV columns: {list(df.columns)}")
            
            collection = self.db.trustActivities
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    trust_data = {
                        'date': self.parse_date(self.safe_get(row, 'Date', '')),
                        'relatedTo': self.safe_get(row, 'Related To', ''),
                        'contact': self.safe_get(row, 'Contact', ''),
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'enteredBy': self.safe_get(row, 'Entered By', ''),
                        'originatingAttorney': self.safe_get(row, 'Originating Attorney', ''),
                        'leadAttorney': self.safe_get(row, 'Lead Attorney', ''),
                        'notes': self.safe_get(row, 'Notes', ''),
                        'paymentNotes': self.safe_get(row, 'Payment Notes', ''),
                        'paymentMethod': self.safe_get(row, 'Payment Method', ''),
                        'refund': self.safe_get(row, 'Refund', '').lower() == 'true',
                        'refunded': self.safe_get(row, 'Refunded', '').lower() == 'true',
                        'rejection': self.safe_get(row, 'Rejection', '').lower() == 'true',
                        'rejected': self.safe_get(row, 'Rejected', '').lower() == 'true',
                        'amount': self.parse_number(self.safe_get(row, 'Amount', '0')),
                        'trust': self.safe_get(row, 'Trust', '').lower() == 'true',
                        'trustPayment': self.safe_get(row, 'Trust payment', '').lower() == 'true',
                        'credit': self.safe_get(row, 'Credit', '').lower() == 'true',
                        'operatingCredit': self.safe_get(row, 'Operating Credit', '').lower() == 'true',
                        'total': self.parse_number(self.safe_get(row, 'Total', '0')),
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if trust_data['myCaseId']:
                        unique_id = f"mycase_{trust_data['myCaseId']}"
                    elif trust_data['date'] and trust_data['caseName'] and trust_data['amount']:
                        # Use date, case name and amount as unique identifier
                        unique_id = f"{trust_data['date']}_{trust_data['caseName']}_{trust_data['amount']}"
                    elif trust_data['date'] and trust_data['contact'] and trust_data['amount']:
                        # Use date, contact and amount as unique identifier
                        unique_id = f"{trust_data['date']}_{trust_data['contact']}_{trust_data['amount']}"
                    else:
                        # Use row index as last resort
                        unique_id = f"row_{index}"
                    
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**trust_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} trust activity records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import trust activity data: {e}")
            import traceback
            traceback.print_exc()

    def import_account_activities(self):
        """Import account activity data"""
        try:
            file_path = os.path.join(self.csv_directory, 'account_activities.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting account activity data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.accountActivities
            imported_count = 0
            
            for _, row in df.iterrows():
                activity_data = {
                    'date': self.parse_date(self.safe_get(row, 'Date', '')),
                    'description': self.safe_get(row, 'Description', ''),
                    'amount': self.parse_number(self.safe_get(row, 'Amount', '0')),
                    'type': self.safe_get(row, 'Type', ''),
                    'reference': self.safe_get(row, 'Reference', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use date and description as unique identifier
                unique_id = f"{activity_data['date']}_{activity_data['description']}"
                if activity_data['date']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**activity_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} account activity records")
            
        except Exception as e:
            logger.error(f"Failed to import account activity data: {e}")
            import traceback
            traceback.print_exc()

    def import_call_logs(self):
        """Import call log data"""
        try:
            file_path = os.path.join(self.csv_directory, 'call_logs.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting call log data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.callLogs
            imported_count = 0
            
            for _, row in df.iterrows():
                call_data = {
                    'date': self.parse_date(self.safe_get(row, 'Date', '')),
                    'phoneNumber': self.safe_get(row, 'Phone Number', ''),
                    'duration': self.safe_get(row, 'Duration', ''),
                    'type': self.safe_get(row, 'Type', ''),
                    'notes': self.safe_get(row, 'Notes', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use date and phone number as unique identifier
                unique_id = f"{call_data['date']}_{call_data['phoneNumber']}"
                if call_data['date'] and call_data['phoneNumber']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**call_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} call log records")
            
        except Exception as e:
            logger.error(f"Failed to import call log data: {e}")
            import traceback
            traceback.print_exc()

    def import_case_stages(self):
        """Import case stage data - fixed field mapping"""
        try:
            file_path = os.path.join(self.csv_directory, 'case_stages.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting case stage data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            case_name_count = df['Case Name'].notna().sum() if 'Case Name' in df.columns else 0
            stage_name_count = df['Stage Name'].notna().sum() if 'Stage Name' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with case name: {case_name_count}")
            logger.info(f"Rows with stage name: {stage_name_count}")
            
            collection = self.db.caseStages
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    stage_data = {
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'stageName': self.safe_get(row, 'Stage Name', ''),
                        'stageBeginningDate': self.parse_date(self.safe_get(row, 'Stage Beginning Date', '')),
                        'stageEndingDate': self.parse_date(self.safe_get(row, 'Stage Ending Date', '')),
                        'daysSpentInStage': self.parse_number(self.safe_get(row, 'Days Spent in Stage', '0')),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Use case name and stage name as unique identifier
                    unique_id = f"{stage_data['caseName']}_{stage_data['stageName']}"
                    if stage_data['caseName'] and stage_data['stageName']:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**stage_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} case stage records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import case stage data: {e}")
            import traceback
            traceback.print_exc()

    def import_emails(self):
        """Import email data"""
        try:
            file_path = os.path.join(self.csv_directory, 'emails.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting email data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.emails
            imported_count = 0
            
            for _, row in df.iterrows():
                email_data = {
                    'date': self.parse_date(self.safe_get(row, 'Date', '')),
                    'from': self.safe_get(row, 'From', ''),
                    'to': self.safe_get(row, 'To', ''),
                    'subject': self.safe_get(row, 'Subject', ''),
                    'body': self.safe_get(row, 'Body', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use date, sender and subject as unique identifier
                unique_id = f"{email_data['date']}_{email_data['from']}_{email_data['subject']}"
                if email_data['date'] and email_data['from']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**email_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} email records")
            
        except Exception as e:
            logger.error(f"Failed to import email data: {e}")
            import traceback
            traceback.print_exc()
            
    def import_flat_fees(self):
        """Import flat fee data - optimized version"""
        try:
            file_path = os.path.join(self.csv_directory, 'flat_fees.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting flat fee data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            case_name_count = df['Case Name'].notna().sum() if 'Case Name' in df.columns else 0
            amount_count = df['Amount'].notna().sum() if 'Amount' in df.columns else 0
            date_count = df['Date'].notna().sum() if 'Date' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with case name: {case_name_count}")
            logger.info(f"Rows with amount: {amount_count}")
            logger.info(f"Rows with date: {date_count}")
            
            collection = self.db.flatFees
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    fee_data = {
                        'date': self.parse_date(self.safe_get(row, 'Date', '')),
                        'amount': self.parse_number(self.safe_get(row, 'Amount', '0')),
                        'description': self.safe_get(row, 'Description', ''),
                        'enteredBy': self.safe_get(row, 'Entered By', ''),
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'invoice': self.safe_get(row, 'Invoice', ''),
                        'nonbillable': self.safe_get(row, 'Nonbillable', '').lower() == 'true',
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if fee_data['myCaseId']:
                        unique_id = f"mycase_{fee_data['myCaseId']}"
                    elif fee_data['caseName'] and fee_data['date'] and fee_data['amount']:
                        # Use case name, date and amount combination
                        unique_id = f"case_{fee_data['caseName']}_{fee_data['date']}_{fee_data['amount']}"
                    elif fee_data['caseName'] and fee_data['date']:
                        # Use case name and date
                        unique_id = f"case_{fee_data['caseName']}_{fee_data['date']}"
                    elif fee_data['caseName'] and fee_data['amount']:
                        # Use case name and amount
                        unique_id = f"case_{fee_data['caseName']}_{fee_data['amount']}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**fee_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} flat fee records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import flat fee data: {e}")
            import traceback
            traceback.print_exc()

    def import_events(self):
        """Import event data"""
        try:
            file_path = os.path.join(self.csv_directory, 'events.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting event data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            name_count = df['Name'].notna().sum() if 'Name' in df.columns else 0
            start_time_count = df['Start Time'].notna().sum() if 'Start Time' in df.columns else 0
            mycase_id_count = df['MyCase ID'].notna().sum() if 'MyCase ID' in df.columns else 0
            
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with event name: {name_count}")
            logger.info(f"Rows with start time: {start_time_count}")
            logger.info(f"Rows with MyCase ID: {mycase_id_count}")
            
            collection = self.db.events
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    event_data = {
                        'name': self.safe_get(row, 'Name', ''),
                        'description': self.safe_get(row, 'Description', ''),
                        'startTime': self.parse_date(self.safe_get(row, 'Start Time', '')),
                        'endTime': self.parse_date(self.safe_get(row, 'End Time', '')),
                        'allDay': self.safe_get(row, 'All day', '').lower() == 'true',
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'location': self.safe_get(row, 'Location', ''),
                        'isPrivate': self.safe_get(row, 'Private?', '').lower() == 'true',
                        'archived': self.safe_get(row, 'Archived', '').lower() == 'true',
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'comments': self.safe_get(row, 'Comments', ''),
                        'sharedWith': self.safe_get(row, 'Shared With', ''),
                        'eventType': self.safe_get(row, 'Event Type', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if event_data['myCaseId']:
                        unique_id = f"mycase_{event_data['myCaseId']}"
                    elif event_data['name'] and event_data['startTime']:
                        unique_id = f"event_{event_data['startTime']}_{event_data['name']}"
                    elif event_data['name']:
                        unique_id = f"name_{event_data['name']}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**event_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                        
                        # Output progress every 1000 records
                        if imported_count % 1000 == 0:
                            logger.info(f"Imported {imported_count} event records...")
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} event records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
            # Verify import results
            total_in_db = collection.count_documents({})
            logger.info(f"Total {total_in_db} event records in database")
            
        except Exception as e:
            logger.error(f"Failed to import event data: {e}")
            import traceback
            traceback.print_exc()

    def import_invoice_discounts(self):
        """Import invoice discount data - optimized version"""
        try:
            file_path = os.path.join(self.csv_directory, 'invoice_discounts.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting invoice discount data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            invoice_count = df['Invoice'].notna().sum() if 'Invoice' in df.columns else 0
            amount_count = df['Amount'].notna().sum() if 'Amount' in df.columns else 0
            case_count = df['Case Name'].notna().sum() if 'Case Name' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with invoice number: {invoice_count}")
            logger.info(f"Rows with amount: {amount_count}")
            logger.info(f"Rows with case name: {case_count}")
            
            collection = self.db.invoiceDiscounts
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    discount_data = {
                        'item': self.safe_get(row, 'Item', ''),
                        'appliedTo': self.safe_get(row, 'Applied To', ''),
                        'type': self.safe_get(row, 'Type', ''),
                        'description': self.safe_get(row, 'Description', ''),
                        'basis': self.parse_number(self.safe_get(row, 'Basis', '0')),
                        'percent': self.parse_number(self.safe_get(row, 'Percent', '0')),
                        'amount': self.parse_number(self.safe_get(row, 'Amount', '0')),
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'invoice': self.safe_get(row, 'Invoice', ''),
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if discount_data['myCaseId']:
                        unique_id = f"mycase_{discount_data['myCaseId']}"
                    elif discount_data['invoice'] and discount_data['description']:
                        # Use invoice number and description combination
                        unique_id = f"invoice_{discount_data['invoice']}_{discount_data['description']}"
                    elif discount_data['invoice'] and discount_data['amount']:
                        # Use invoice number and amount combination
                        unique_id = f"invoice_{discount_data['invoice']}_{discount_data['amount']}"
                    elif discount_data['caseName'] and discount_data['description']:
                        # Use case name and description combination
                        unique_id = f"case_{discount_data['caseName']}_{discount_data['description']}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**discount_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} invoice discount records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import invoice discount data: {e}")
            import traceback
            traceback.print_exc()

    def import_locations(self):
        """Import location data"""
        try:
            file_path = os.path.join(self.csv_directory, 'locations.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting location data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            collection = self.db.locations
            imported_count = 0
            
            for _, row in df.iterrows():
                location_data = {
                    'name': self.safe_get(row, 'Name', ''),
                    'address': self.safe_get(row, 'Address', ''),
                    'city': self.safe_get(row, 'City', ''),
                    'state': self.safe_get(row, 'State', ''),
                    'zipCode': self.safe_get(row, 'Zip Code', ''),
                    'phone': self.safe_get(row, 'Phone', ''),
                    'createdAt': datetime.now(),
                    'updatedAt': datetime.now()
                }
                
                # Use name and address as unique identifier
                unique_id = f"{location_data['name']}_{location_data['address']}"
                if location_data['name']:
                    collection.update_one(
                        {'uniqueId': unique_id},
                        {'$set': {**location_data, 'uniqueId': unique_id}},
                        upsert=True
                    )
                    imported_count += 1
            
            logger.info(f"Successfully imported {imported_count} location records")
            
        except Exception as e:
            logger.error(f"Failed to import location data: {e}")
            import traceback
            traceback.print_exc()

    def import_messages(self):
        """Import message data - optimized version with correct CSV field parsing"""
        try:
            file_path = os.path.join(self.csv_directory, 'messages.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting message data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            sender_count = df['Sender'].notna().sum() if 'Sender' in df.columns else 0
            subject_count = df['Subject'].notna().sum() if 'Subject' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with sender: {sender_count}")
            logger.info(f"Rows with subject: {subject_count}")
            
            collection = self.db.messages
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    # Parse sent time
                    sent_at = self.safe_get(row, 'Sent At', '')
                    sent_date = None
                    if sent_at:
                        # Handle format: "2022-04-05 19:34:20 -0700"
                        try:
                            # Remove timezone info, keep only datetime part
                            sent_at_clean = sent_at.split(' -')[0] if ' -' in sent_at else sent_at
                            sent_date = self.parse_date(sent_at_clean)
                        except:
                            sent_date = None
                    
                    # Parse last reply time
                    last_post_at = self.safe_get(row, 'Last post at', '')
                    last_post_date = None
                    if last_post_at:
                        try:
                            last_post_at_clean = last_post_at.split(' -')[0] if ' -' in last_post_at else last_post_at
                            last_post_date = self.parse_date(last_post_at_clean)
                        except:
                            last_post_date = None
                    
                    message_data = {
                        'sender': self.safe_get(row, 'Sender', ''),
                        'subject': self.safe_get(row, 'Subject', ''),
                        'sentAt': sent_date,
                        'lastPostAt': last_post_date,
                        'globalClients': self.safe_get(row, 'Global clients', '').lower() == 'true',
                        'globalLawyers': self.safe_get(row, 'Global lawyers', '').lower() == 'true',
                        'privateReply': self.safe_get(row, 'Private reply', '').lower() == 'true',
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'myCaseId': self.safe_get(row, 'MyCase ID', ''),
                        'numberOfMessages': self.parse_number(self.safe_get(row, 'Number of Messages', '0')),
                        'messages': self.safe_get(row, 'Messages', ''),
                        'sentTo': self.safe_get(row, 'Sent To', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize MyCase ID
                    unique_id = None
                    if message_data['myCaseId']:
                        unique_id = f"mycase_{message_data['myCaseId']}"
                    elif message_data['sender'] and message_data['subject'] and message_data['sentAt']:
                        # Use sender, subject and sent time combination
                        unique_id = f"sender_{message_data['sender']}_{message_data['subject']}_{message_data['sentAt']}"
                    elif message_data['sender'] and message_data['sentAt']:
                        # Use sender and sent time combination
                        unique_id = f"sender_{message_data['sender']}_{message_data['sentAt']}"
                    
                    if unique_id:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**message_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} message records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import message data: {e}")
            import traceback
            traceback.print_exc()


    def import_status_updates(self):
        """Import status update data - optimized version with correct CSV field parsing"""
        try:
            file_path = os.path.join(self.csv_directory, 'status_updates.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting status update data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            case_name_count = df['Case Name'].notna().sum() if 'Case Name' in df.columns else 0
            status_count = df['Status Update'].notna().sum() if 'Status Update' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with case name: {case_name_count}")
            logger.info(f"Rows with status update: {status_count}")
            
            collection = self.db.statusUpdates
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    # Parse creation time
                    created_at = self.safe_get(row, 'Created Date/Time', '')
                    created_date = None
                    if created_at:
                        # Handle format: "2024-10-04 12:09:10 -0700"
                        try:
                            # Remove timezone info, keep only datetime part
                            created_at_clean = created_at.split(' -')[0] if ' -' in created_at else created_at
                            created_date = self.parse_date(created_at_clean)
                        except:
                            created_date = None
                    
                    # Parse update time
                    updated_at = self.safe_get(row, 'Updated Date/Time', '')
                    updated_date = None
                    if updated_at:
                        try:
                            updated_at_clean = updated_at.split(' -')[0] if ' -' in updated_at else updated_at
                            updated_date = self.parse_date(updated_at_clean)
                        except:
                            updated_date = None
                    
                    status_data = {
                        'caseName': self.safe_get(row, 'Case Name', ''),
                        'statusUpdate': self.safe_get(row, 'Status Update', ''),
                        'createdDate': created_date,
                        'createdBy': self.safe_get(row, 'Created By', ''),
                        'updatedDate': updated_date,
                        'updatedBy': self.safe_get(row, 'Updated By', ''),
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - prioritize case name and creation time
                    unique_id = None
                    if status_data['caseName'] and status_data['createdDate']:
                        unique_id = f"case_{status_data['caseName']}_{status_data['createdDate']}"
                    elif status_data['caseName'] and status_data['createdBy']:
                        unique_id = f"case_{status_data['caseName']}_{status_data['createdBy']}"
                    elif status_data['caseName']:
                        unique_id = f"case_{status_data['caseName']}"
                    
                    if unique_id and status_data['caseName']:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**status_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} status update records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import status update data: {e}")
            import traceback
            traceback.print_exc()


    def import_text_messages(self):
        """Import text message data - optimized version with correct CSV field parsing"""
        try:
            file_path = os.path.join(self.csv_directory, 'text_messages.csv')
            if not os.path.exists(file_path):
                logger.warning(f"File does not exist: {file_path}")
                return
            
            logger.info("Starting text message data import...")
            df = pd.read_csv(file_path)
            df = self.clean_data(df)
            
            # Analyze data
            total_rows = len(df)
            phone_count = df['Phone Number'].notna().sum() if 'Phone Number' in df.columns else 0
            date_count = df['Date/Time'].notna().sum() if 'Date/Time' in df.columns else 0
            logger.info(f"CSV file contains {total_rows} rows")
            logger.info(f"Rows with phone number: {phone_count}")
            logger.info(f"Rows with date: {date_count}")
            
            collection = self.db.textMessages
            imported_count = 0
            skipped_count = 0
            
            for index, row in df.iterrows():
                try:
                    # Parse datetime - handle format: "2022-10-20 09:04:40 -0700"
                    date_time_str = self.safe_get(row, 'Date/Time', '')
                    parsed_date = None
                    if date_time_str:
                        try:
                            # Remove timezone info, keep only datetime part
                            date_time_clean = date_time_str.split(' -')[0] if ' -' in date_time_str else date_time_str
                            parsed_date = self.parse_date(date_time_clean)
                        except:
                            parsed_date = None
                    
                    # Determine message direction
                    sender = self.safe_get(row, 'Sender', '')
                    phone_number = self.safe_get(row, 'Phone Number', '')
                    direction = 'inbound' if sender == phone_number else 'outbound'
                    
                    text_data = {
                        'contactName': self.safe_get(row, 'Contact Name', ''),
                        'caseNames': self.safe_get(row, 'Case Name(s)', ''),
                        'phoneNumber': phone_number,
                        'message': self.safe_get(row, 'Message', ''),
                        'dateTime': parsed_date,
                        'sender': sender,
                        'direction': direction,
                        'createdAt': datetime.now(),
                        'updatedAt': datetime.now()
                    }
                    
                    # Create unique identifier - use datetime and phone number
                    unique_id = None
                    if parsed_date and phone_number:
                        unique_id = f"{parsed_date}_{phone_number}_{index}"
                    elif phone_number:
                        unique_id = f"{phone_number}_{index}"
                    
                    if unique_id and phone_number:
                        collection.update_one(
                            {'uniqueId': unique_id},
                            {'$set': {**text_data, 'uniqueId': unique_id}},
                            upsert=True
                        )
                        imported_count += 1
                    else:
                        skipped_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing row {index}: {e}")
                    skipped_count += 1
                    continue
            
            logger.info(f"Successfully imported {imported_count} text message records")
            if skipped_count > 0:
                logger.info(f"Skipped {skipped_count} invalid records")
            
        except Exception as e:
            logger.error(f"Failed to import text message data: {e}")
            import traceback
            traceback.print_exc()
    
    def import_all_data(self) -> bool:
        """Import all data"""
        if not self.connect_mongo():
            return False
        
        try:
            logger.info("Starting all data import...")
            
            self.import_cases()
            self.import_people()
            self.import_companies()
            self.import_lawyers() 
            self.import_leads()
            self.import_tasks()
            self.import_documents()
            self.import_time_entries()
            self.import_expenses()
            self.import_invoices()
            self.import_notes()
            self.import_trust_activities()
            self.import_account_activities()
            self.import_call_logs()
            self.import_case_stages()
            self.import_emails()
            self.import_events()
            self.import_flat_fees()
            self.import_invoice_discounts()
            self.import_locations()
            self.import_messages()
            self.import_status_updates()
            self.import_text_messages()
            
            logger.info("All data import complete!")
            return True
            
        except Exception as e:
            logger.error(f"Error during import: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.close_mongo()
    
    def get_import_summary(self) -> Dict[str, int]:
        """Get import summary"""
        if not self.connect_mongo():
            return {}
        
        try:
            summary = {}
            collections = [
                'cases', 'contacts', 'companies', 'lawyers', 'leads', 
                'tasks', 'documents', 'timeEntries', 'expenses', 
                'invoices', 'notes', 'trustActivities',
                'accountActivities', 'callLogs', 'caseStages', 'emails',
                'events', 'flatFees', 'invoiceDiscounts', 'locations',
                'messages', 'statusUpdates', 'textMessages'
            ]
            
            for collection_name in collections:
                count = self.db[collection_name].count_documents({})
                summary[collection_name] = count
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get summary: {e}")
            return {}
        finally:
            self.close_mongo()


def main():
    """Main function"""
    # Configuration parameters
    csv_directory = "mycase-full-backup-08-05-2025"
    mongo_uri = "mongodb://localhost:27017/"
    db_name = "mycase"
    
    # Check if CSV directory exists
    if not os.path.exists(csv_directory):
        logger.error(f"CSV directory does not exist: {csv_directory}")
        sys.exit(1)
    
    # Create importer
  
    importer = MyCaseMongoImporter(csv_directory, mongo_uri, db_name)
    
   # Import all data
    success = importer.import_all_data()
    
    if success:
        # Display import summary
        summary = importer.get_import_summary()
        if summary:
            logger.info("Import summary:")
            total_records = 0
            for collection, count in summary.items():
                logger.info(f"  {collection}: {count} records")
                total_records += count
            logger.info(f"Total: {total_records} records")

logger.info("Fixed import script execution complete!")

if __name__ == "__main__":
    main()
