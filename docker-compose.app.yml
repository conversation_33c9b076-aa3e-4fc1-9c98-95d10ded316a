version: '3.8'

services:
  backend:
    build: ./backend
    container_name: mycase_backend
    restart: unless-stopped
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://host.docker.internal:27017/mycase
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - FRONTEND_URL=http://localhost:3000
      - PORT=5001
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
    extra_hosts:
      - "host.docker.internal:host-gateway"

  frontend:
    build: ./frontend
    container_name: mycase_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5001/api
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    stdin_open: true
    tty: true
