# 榴莲律师事务所管理系统 - 软件需求规格说明书

---

### **1. 引言**

#### **1.1 目的**
本文档旨在详细描述“榴莲律师事务所管理系统”的功能性与非功能性需求。它将作为项目开发、测试和验收的主要依据，确保所有相关方对系统有统一、清晰的理解。

#### **1.2 项目范围**
本项目旨在开发一个集成的、基于Web的律师事务所管理平台。系统将覆盖从销售线索跟进、案件全生命周期管理、任务与日程协同，到精细化的财务账单处理等核心业务流程。其目标是取代或优化现有的、可能较为分散的管理工具（如 MyCase），为律师、法务助理和管理人员提供一个高效、统一的工作环境。

#### **1.3 目标用户**
*   **律师 (Attorney)**: 案件的主要负责人，关注案件进展、日程安排和工时记录。
*   **法务助理 (Paralegal)**: 协助律师处理案件相关的任务、文档和客户沟通。
*   **管理人员 (Admin/Manager)**: 关注律所整体的运营状况、财务报告和员工效率。

---

### **2. 整体描述**

#### **2.1 产品愿景**
打造一个一站式的律所业务管理解决方案，通过数字化和自动化的手段，将繁琐的行政和管理工作流程化、简单化，让法律专业人士能更专注于其核心业务，最终提升整个律所的生产力和客户满意度。

#### **2.2 核心业务流程图**
此流程图展示了从一个潜在客户（线索）到案件完结并收款的核心业务闭环。

```mermaid
graph TD
    A[潜在客户咨询] --> B{创建销售线索 Lead};
    B --> C{跟进与沟通};
    C --> D{线索转化为案件?};
    D -- 是 --> E[创建案件 Case];
    D -- 否 --> F[线索关闭];
    E --> G[分配律师/团队];
    G --> H{案件处理};
    H --> I[记录工时 Time Entry];
    H --> J[记录开销 Expense];
    H --> K[创建任务 Task];
    H --> L[安排日历事件 Calendar];
    I & J --> M{生成发票 Invoice};
    M --> N[发送给客户];
    N --> O{客户付款};
    O --> P[记录付款 Payment];
    P --> Q{案件完结?};
    Q -- 是 --> R[归档案件];
    Q -- 否 --> H;
```

---

### **3. 功能需求**

#### **3.1 案件管理 (Cases)**
*   **功能描述**: 提供对所有案件进行创建、追踪、管理和归档的核心功能。
*   **用户故事**:
    *   **As a** 律师, **I want to** 快速查看我负责的所有案件及其当前状态, **so that** 我可以有效地规划我的工作优先级。
    *   **As a** 法务助理, **I want to** 在案件详情页集中查看所有相关信息（如文档、笔记、任务）, **so that** 我能快速找到所需材料，为律师提供支持。
*   **详细需求**:
    *   系统必须提供一个案件列表页面 (`Cases.js`)，支持搜索、筛选（按案件状态、负责人等）。
    *   每个案件都有一个唯一的详情页面 (`CaseDetail.js`)。
    *   案件详情页必须聚合展示该案件下的所有关联信息：账单、任务、文档、笔记、日历事件等。
    *   支持自定义案件阶段 (Case Stage)，如“初步接洽”、“证据收集中”、“庭审阶段”、“已结案”等。

#### **3.2 财务与账单 (Billing & Financials)**
*   **功能描述**: 管理与案件相关的所有财务活动，包括工时、开销、发票和付款。
*   **用户故事**:
    *   **As a** 律师, **I want to** 方便地记录我在每个案件上花费的时间, **so that** 确保所有可计费工时都被准确记录和开具发票。
    *   **As a** 管理人员, **I want to** 查看每个案件的财务概览（如合同金额、已收款、未付余额）, **so that** 我能实时掌握律所的现金流状况。
*   **详细需求**:
    *   **工时记录 (TimeEntries.js)**: 允许用户记录工时，并关联到具体案件。
    *   **开销记录 (Expenses.js)**: 允许用户记录案件相关的支出。
    *   **发票生成 (Invoices.js)**: 系统应能根据已记录的工时和开销，自动或手动生成发票。
    *   **账单概览 (Billing.js)**: 提供一个统一的账单页面，展示信托账户活动、付款历史和发票列表。

#### **3.3 任务与日历 (Tasks & Calendar)**
*   **功能描述**: 团队协作的核心，用于分配任务和安排日程。
*   **用户故事**:
    *   **As a** 法务助理, **I want to** 查看分配给我的所有任务和截止日期, **so that** 我不会遗漏任何重要工作。
    *   **As a** 律师, **I want to** 在日历上查看所有与我相关的开庭日期和会议安排, **so that** 我可以避免日程冲突。
*   **详细需求**:
    *   **任务管理 (Tasks.js)**: 任务可以被创建、分配给特定员工，并关联到案件。支持设置截止日期和优先级。
    *   **日历视图 (Calendar.js)**: 所有与案件关联的事件都应在日历上显示。支持按员工筛选视图，并用不同颜色区分员工日程。

---

### **4. 非功能需求**

#### **4.1 性能**
*   所有页面的平均加载时间应在3秒以内。
*   列表页（如案件、联系人）的搜索和筛选响应时间应在1秒以内。

#### **4.2 安全性**
*   用户密码必须经过加密存储。
*   所有客户端与服务器之间的通信必须使用 HTTPS 加密。
*   应有角色权限控制，确保用户只能访问其被授权的数据和功能。

#### **4.3 易用性**
*   界面设计应简洁、直观，符合主流 Web 应用的操作习惯。
*   导航栏结构清晰，用户可以快速找到所需功能。

---

### **5. 接口需求**

#### **5.1 用户界面 (UI)**
*   系统应采用响应式设计，适配主流的桌面浏览器（Chrome, Firefox, Safari）。
*   UI 风格应保持一致性，包括颜色、字体、图标和按钮样式。

#### **5.2 应用编程接口 (API)**
*   后端应提供 RESTful 风格的 API。
*   所有 API 响应数据格式统一为 JSON。
*   API 文档应清晰、完整，便于前端开发和未来集成。
