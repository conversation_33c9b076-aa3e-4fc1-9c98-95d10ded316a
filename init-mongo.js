db = db.getSiblingDB('mycase');

// Create default admin user with properly hashed password (admin123)
db.users.insertOne({
  email: '<EMAIL>',
  password: '$2a$10$FUzeY.uEQ9j6uYI643pzp.Vk3VL.4r3RSGkYSbpFZA/U6pkfIe1u2',
  firstName: 'System',
  lastName: 'Administrator',
  role: 'admin',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

print('Database initialized with default admin user');
print('Email: <EMAIL>');
print('Password: admin123');