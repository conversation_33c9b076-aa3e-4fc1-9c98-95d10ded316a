const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');

// Import models
const Expense = require('../models/Expense');
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper function to parse dates
function parseDate(dateString) {
  if (!dateString) return null;
  
  // Try MM/DD/YYYY format first
  const mmddyyyy = dateString.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
  if (mmddyyyy) {
    const [, month, day, year] = mmddyyyy;
    return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  }
  
  // Try other formats
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

// Helper function to parse numbers
function parseNumber(numberString) {
  if (!numberString) return 0;
  return parseFloat(numberString.toString().replace(/[^\d.-]/g, '')) || 0;
}

// Helper function to parse booleans
function parseBoolean(boolString) {
  if (!boolString) return false;
  return boolString.toString().toLowerCase() === 'true';
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

// Get category mapping
function getCategoryFromActivity(activity) {
  if (!activity) return 'other';
  const activityLower = activity.toLowerCase();
  
  if (activityLower.includes('postage') || activityLower.includes('mail')) return 'postage';
  if (activityLower.includes('filing') || activityLower.includes('file')) return 'filing_fees';
  if (activityLower.includes('copy') || activityLower.includes('copying')) return 'copying';
  if (activityLower.includes('travel')) return 'travel';
  if (activityLower.includes('research')) return 'research';
  if (activityLower.includes('expert')) return 'expert_fees';
  
  return 'other';
}

async function migrateExpenses() {
  try {
    await connectDB();
    
    console.log('🚀 Starting expense migration...');
    
    // Build case and user mappings
    console.log('📋 Building case and user mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      const fullName = caseItem.caseName || '';
      caseMap.set(fullName, caseItem._id);
    });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${cases.length} cases and ${users.length} users for mapping`);
    
    // Read expenses CSV
    const expenses = await readCSV('/app/backup-data/expenses.csv');
    console.log(`📄 Found ${expenses.length} expense records to process`);
    
    let imported = 0;
    let errors = 0;
    let skipped = 0;
    
    for (const expenseData of expenses) {
      try {
        // Find matching case
        const caseName = expenseData['Case Name'] || '';
        const caseId = caseMap.get(caseName);
        
        if (!caseId) {
          console.log(`⚠️ Skipping expense: No matching case found for "${caseName}"`);
          skipped++;
          continue;
        }
        
        // Find matching user
        const userName = expenseData['User'] || '';
        const userId = userMap.get(userName);
        
        if (!userId) {
          console.log(`⚠️ Skipping expense: No matching user found for "${userName}"`);
          skipped++;
          continue;
        }
        
        // Check if expense already exists
        const existingExpense = await Expense.findOne({
          mycase_id: expenseData['MyCase ID']
        });
        
        if (existingExpense) {
          skipped++;
          continue;
        }
        
        // Create expense
        const newExpense = new Expense({
          date: parseDate(expenseData['Date']) || new Date(),
          activity: expenseData['Activity'] || 'Expense',
          quantity: parseNumber(expenseData['Quantity']) || 1,
          cost: parseNumber(expenseData['Cost']) || 0,
          total: parseNumber(expenseData['Total']) || 0,
          description: expenseData['Description'] || expenseData['Activity'] || 'Expense',
          user: userId,
          caseId: caseId,
          nonbillable: parseBoolean(expenseData['Nonbillable']),
          billed: false, // Default to not billed
          category: getCategoryFromActivity(expenseData['Activity']),
          mycase_id: expenseData['MyCase ID']
        });
        
        await newExpense.save();
        imported++;
        
        if (imported % 50 === 0) {
          console.log(`📊 Processed ${imported} expenses...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing expense:`, error.message);
        errors++;
      }
    }
    
    console.log('\n🎉 EXPENSE MIGRATION COMPLETED!');
    console.log(`✅ Successfully imported: ${imported} expenses`);
    console.log(`⚠️ Skipped: ${skipped} expenses`);
    console.log(`❌ Errors: ${errors} expenses`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateExpenses();