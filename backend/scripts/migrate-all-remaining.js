const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Note = require('../models/Note');
const Task = require('../models/Task');
const TrustActivity = require('../models/TrustActivity');
const FlatFee = require('../models/FlatFee');
const CaseStage = require('../models/CaseStage');
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseDate(dateString) {
  if (!dateString) return null;
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

function parseNumber(numberString) {
  if (!numberString) return 0;
  return parseFloat(numberString.toString().replace(/[^\d.-]/g, '')) || 0;
}

function cleanHtmlContent(htmlString) {
  if (!htmlString) return '';
  return htmlString
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .trim();
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function migrateAllRemaining() {
  try {
    await connectDB();
    
    console.log('🚀 STARTING COMPREHENSIVE REMAINING DATA MIGRATION');
    console.log('====================================================');
    
    // Build mappings once
    console.log('📋 Building case and user mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${cases.length} cases and ${users.length} users for mapping`);
    
    const stats = {
      notes: { imported: 0, skipped: 0, errors: 0 },
      trustActivities: { imported: 0, skipped: 0, errors: 0 },
      flatFees: { imported: 0, skipped: 0, errors: 0 },
      caseStages: { imported: 0, skipped: 0, errors: 0 }
    };

    // MIGRATE NOTES
    console.log('\n📝 MIGRATING NOTES...');
    try {
      const notes = await readCSV('/app/backup-data/notes.csv');
      console.log(`📄 Found ${notes.length} note records to process`);
      
      for (const noteData of notes) {
        try {
          const caseName = noteData['Case Name'] || '';
          const caseId = caseMap.get(caseName);
          
          if (!caseId) {
            stats.notes.skipped++;
            continue;
          }
          
          const createdByName = noteData['Created By'] || '';
          const createdBy = userMap.get(createdByName);
          
          if (!createdBy) {
            stats.notes.skipped++;
            continue;
          }
          
          const content = cleanHtmlContent(noteData['Note']) || noteData['Subject'] || 'No content';
          const subject = noteData['Subject'] || 'Note';
          const noteDate = parseDate(noteData['Date']) || parseDate(noteData['Created at']) || new Date();
          
          const newNote = new Note({
            subject,
            content,
            caseId,
            createdBy,
            noteDate,
            mycase_id: `note_${stats.notes.imported + 1}`
          });
          
          await newNote.save();
          stats.notes.imported++;
          
        } catch (error) {
          stats.notes.errors++;
        }
      }
    } catch (error) {
      console.error('Error in notes migration:', error);
    }

    // MIGRATE TRUST ACTIVITIES  
    console.log('\n💰 MIGRATING TRUST ACTIVITIES...');
    try {
      const trustActivities = await readCSV('/app/backup-data/trust_activities.csv');
      console.log(`📄 Found ${trustActivities.length} trust activity records to process`);
      
      for (const trustData of trustActivities) {
        try {
          const caseName = trustData['Case Name'] || '';
          const caseId = caseMap.get(caseName);
          
          if (!caseId) {
            stats.trustActivities.skipped++;
            continue;
          }
          
          const createdByName = trustData['User'] || '';
          const createdBy = userMap.get(createdByName);
          
          if (!createdBy) {
            stats.trustActivities.skipped++;
            continue;
          }
          
          const newTrustActivity = new TrustActivity({
            date: parseDate(trustData['Date']) || new Date(),
            description: trustData['Description'] || 'Trust Activity',
            type: trustData['Type']?.toLowerCase() || 'deposit',
            amount: parseNumber(trustData['Amount']),
            balance: parseNumber(trustData['Balance']),
            caseId,
            user: createdBy,
            mycase_id: trustData['ID'] || `trust_${stats.trustActivities.imported + 1}`
          });
          
          await newTrustActivity.save();
          stats.trustActivities.imported++;
          
        } catch (error) {
          stats.trustActivities.errors++;
        }
      }
    } catch (error) {
      console.error('Error in trust activities migration:', error);
    }

    // MIGRATE FLAT FEES
    console.log('\n💵 MIGRATING FLAT FEES...');
    try {
      const flatFees = await readCSV('/app/backup-data/flat_fees.csv');
      console.log(`📄 Found ${flatFees.length} flat fee records to process`);
      
      for (const feeData of flatFees) {
        try {
          const caseName = feeData['Case Name'] || '';
          const caseId = caseMap.get(caseName);
          
          if (!caseId) {
            stats.flatFees.skipped++;
            continue;
          }
          
          const createdByName = feeData['User'] || '';
          const createdBy = userMap.get(createdByName);
          
          if (!createdBy) {
            stats.flatFees.skipped++;
            continue;
          }
          
          const newFlatFee = new FlatFee({
            description: feeData['Description'] || 'Flat Fee',
            amount: parseNumber(feeData['Amount']),
            caseId,
            user: createdBy,
            date: parseDate(feeData['Date']) || new Date(),
            billed: feeData['Billed']?.toLowerCase() === 'true',
            mycase_id: feeData['ID'] || `fee_${stats.flatFees.imported + 1}`
          });
          
          await newFlatFee.save();
          stats.flatFees.imported++;
          
        } catch (error) {
          stats.flatFees.errors++;
        }
      }
    } catch (error) {
      console.error('Error in flat fees migration:', error);
    }

    // MIGRATE CASE STAGES
    console.log('\n📋 MIGRATING CASE STAGES...');
    try {
      const caseStages = await readCSV('/app/backup-data/case_stages.csv');
      console.log(`📄 Found ${caseStages.length} case stage records to process`);
      
      for (const stageData of caseStages) {
        try {
          const caseName = stageData['Case Name'] || '';
          const caseId = caseMap.get(caseName);
          
          if (!caseId) {
            stats.caseStages.skipped++;
            continue;
          }
          
          const newCaseStage = new CaseStage({
            name: stageData['Stage Name'] || 'Case Stage',
            description: stageData['Description'] || '',
            caseId,
            startDate: parseDate(stageData['Start Date']),
            endDate: parseDate(stageData['End Date']),
            status: stageData['Status']?.toLowerCase() || 'active',
            mycase_id: stageData['ID'] || `stage_${stats.caseStages.imported + 1}`
          });
          
          await newCaseStage.save();
          stats.caseStages.imported++;
          
        } catch (error) {
          stats.caseStages.errors++;
        }
      }
    } catch (error) {
      console.error('Error in case stages migration:', error);
    }
    
    console.log('\n🎉 COMPREHENSIVE MIGRATION COMPLETED!');
    console.log('=====================================');
    console.log(`📝 Notes: ${stats.notes.imported} imported, ${stats.notes.skipped} skipped, ${stats.notes.errors} errors`);
    console.log(`💰 Trust Activities: ${stats.trustActivities.imported} imported, ${stats.trustActivities.skipped} skipped, ${stats.trustActivities.errors} errors`);
    console.log(`💵 Flat Fees: ${stats.flatFees.imported} imported, ${stats.flatFees.skipped} skipped, ${stats.flatFees.errors} errors`);
    console.log(`📋 Case Stages: ${stats.caseStages.imported} imported, ${stats.caseStages.skipped} skipped, ${stats.caseStages.errors} errors`);
    
    const totalImported = stats.notes.imported + stats.trustActivities.imported + stats.flatFees.imported + stats.caseStages.imported;
    console.log(`\n🎯 TOTAL IMPORTED: ${totalImported} records`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateAllRemaining();