#!/usr/bin/env node

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
const MigrationUtility = require('../utils/migration');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/durian_law');
    console.log('MongoDB connected for migration');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Main migration function
const runMigration = async () => {
  console.log('=== Durian Law Data Migration Tool ===\n');
  
  // Get backup path from command line arguments
  const backupPath = process.argv[2];
  
  if (!backupPath) {
    console.error('Error: Please provide the path to the backup directory');
    console.log('Usage: node migrate-backup.js <path-to-backup-directory>');
    process.exit(1);
  }
  
  // Verify backup path exists
  if (!fs.existsSync(backupPath)) {
    console.error(`Error: Backup directory not found: ${backupPath}`);
    process.exit(1);
  }
  
  console.log(`Using backup directory: ${backupPath}`);
  
  try {
    // Connect to database
    await connectDB();
    
    // Create migration utility
    const migration = new MigrationUtility(backupPath);
    
    // Ask for confirmation before proceeding
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const confirmation = await new Promise((resolve) => {
      rl.question('This will import data from the backup. Continue? (y/N): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
    
    if (!confirmation) {
      console.log('Migration cancelled by user');
      process.exit(0);
    }
    
    // Run the migration
    console.log('\nStarting migration process...\n');
    const result = await migration.runFullMigration();
    
    if (result.success) {
      console.log('\n=== Migration Completed Successfully ===');
      console.log(`Total processed: ${result.stats.processed}`);
      console.log(`Total imported: ${result.stats.imported}`);
      console.log(`Total skipped: ${result.stats.skipped}`);
      console.log(`Total errors: ${result.stats.errors}`);
      
      if (result.errors.length > 0) {
        console.log('\nErrors encountered during migration:');
        result.errors.forEach((error, index) => {
          console.log(`${index + 1}. ${error}`);
        });
      }
      
      // Save migration log to file
      const logPath = path.join(__dirname, '../logs/migration-log.txt');
      const logDir = path.dirname(logPath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      fs.writeFileSync(logPath, result.log.join('\n'));
      console.log(`\nMigration log saved to: ${logPath}`);
      
    } else {
      console.log('\n=== Migration Failed ===');
      console.log(`Error: ${result.error}`);
      
      if (result.errors.length > 0) {
        console.log('\nDetailed errors:');
        result.errors.forEach((error, index) => {
          console.log(`${index + 1}. ${error}`);
        });
      }
    }
    
  } catch (error) {
    console.error('Migration script error:', error);
    result = { success: false, error: error.message };
  } finally {
    // Close database connection
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
    process.exit((result && result.success) ? 0 : 1);
  }
};

// Run if called directly
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };