const mongoose = require('mongoose');
const fs = require('fs');
const csv = require('csv-parser');
const bcrypt = require('bcryptjs');

// Import all models
const User = require('../models/User');
const Contact = require('../models/Contact');
const Case = require('../models/Case');
const TimeEntry = require('../models/TimeEntry');
const Expense = require('../models/Expense');
const Invoice = require('../models/Invoice');
const Task = require('../models/Task');
const TrustActivity = require('../models/TrustActivity');
const FlatFee = require('../models/FlatFee');
const CaseStage = require('../models/CaseStage');
const Lead = require('../models/Lead');

class ComprehensiveMigration {
  constructor() {
    this.stats = {
      users: { processed: 0, imported: 0, errors: 0 },
      contacts: { processed: 0, imported: 0, errors: 0 },
      cases: { processed: 0, imported: 0, errors: 0 },
      timeEntries: { processed: 0, imported: 0, errors: 0 },
      expenses: { processed: 0, imported: 0, errors: 0 },
      invoices: { processed: 0, imported: 0, errors: 0 },
      tasks: { processed: 0, imported: 0, errors: 0 },
      trustActivities: { processed: 0, imported: 0, errors: 0 },
      flatFees: { processed: 0, imported: 0, errors: 0 },
      caseStages: { processed: 0, imported: 0, errors: 0 },
      leads: { processed: 0, imported: 0, errors: 0 }
    };
    this.userMap = new Map(); // Map old MyCase IDs to new MongoDB IDs
    this.contactMap = new Map();
    this.caseMap = new Map();
  }

  async connectDB() {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://mongo:27017/durian_law');
    console.log('✅ Connected to MongoDB');
  }

  async readCSV(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      if (!fs.existsSync(filePath)) {
        console.log(`⚠️  File not found: ${filePath}`);
        return resolve([]);
      }
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', reject);
    });
  }

  parseDate(dateStr) {
    if (!dateStr || dateStr === 'NULL' || dateStr === '') return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  parseNumber(numStr) {
    if (!numStr || numStr === 'NULL' || numStr === '') return 0;
    const num = parseFloat(numStr);
    return isNaN(num) ? 0 : num;
  }

  parseBoolean(boolStr) {
    return ['true', '1', 'yes'].includes(String(boolStr).toLowerCase());
  }

  // STEP 1: Import Users/Lawyers
  async migrateUsers() {
    console.log('\n📋 STEP 1: Migrating Users/Lawyers...');
    const lawyers = await this.readCSV('/app/backup-data/lawyers.csv');
    
    for (const lawyer of lawyers) {
      try {
        this.stats.users.processed++;
        
        if (!lawyer['First name'] && !lawyer['Last name']) {
          continue;
        }

        const hashedPassword = await bcrypt.hash('password123', 10);
        
        const userData = {
          firstName: lawyer['First name'] || 'Unknown',
          lastName: lawyer['Last name'] || 'User',
          email: lawyer['Email'] || `user${lawyer['MyCase ID']}@migrated.local`,
          password: hashedPassword,
          role: this.mapUserRole(lawyer['User Type']),
          isActive: !this.parseBoolean(lawyer['Archived']),
          phone: lawyer['Cell phone'] || lawyer['Work phone'] || lawyer['Home phone'] || '',
          hourlyRate: this.parseNumber(lawyer['Default rate'])
        };

        const existingUser = await User.findOne({ email: userData.email });
        if (existingUser) {
          this.userMap.set(lawyer['MyCase ID'], existingUser._id);
          continue;
        }

        const newUser = new User(userData);
        await newUser.save();
        
        this.userMap.set(lawyer['MyCase ID'], newUser._id);
        this.stats.users.imported++;
        
        console.log(`✓ Imported user: ${newUser.firstName} ${newUser.lastName}`);
        
      } catch (error) {
        this.stats.users.errors++;
        console.error(`❌ User error: ${error.message}`);
      }
    }
    
    console.log(`📊 Users: ${this.stats.users.imported} imported, ${this.stats.users.errors} errors`);
  }

  // STEP 2: Import All Contacts
  async migrateContacts() {
    console.log('\n👥 STEP 2: Migrating Contacts...');
    const contacts = await this.readCSV('/app/backup-data/people.csv');
    
    for (const contact of contacts) {
      try {
        this.stats.contacts.processed++;
        
        if (!contact['First Name'] && !contact['Last Name'] && !contact['Company']) {
          continue;
        }

        const existingContact = await Contact.findOne({ mycase_id: contact['MyCase ID'] });
        if (existingContact) {
          this.contactMap.set(contact['MyCase ID'], existingContact._id);
          continue;
        }
        
        const contactData = {
          firstName: contact['First Name'] || '',
          lastName: contact['Last Name'] || '',
          middleName: contact['Middle Name'] || '',
          company: contact['Company'] || '',
          jobTitle: contact['Job Title'] || '',
          email: contact['E-mail Address'] || '',
          phones: {
            home: contact['Home Phone'] || '',
            work: contact['Work Phone'] || '',
            mobile: contact['Mobile Phone'] || '',
            fax: contact['Home Fax'] || ''
          },
          homeAddress: {
            street: contact['Home Street'] || '',
            street2: contact['Home Street 2'] || '',
            city: contact['Home City'] || '',
            state: contact['Home State'] || '',
            postalCode: contact['Home Postal Code'] || '',
            country: contact['Home Country/Region'] || 'USA'
          },
          webPage: contact['Web Page'] || '',
          contactGroup: contact['Contact Group'] || '',
          birthday: this.parseDate(contact['Birthday']),
          privateNotes: contact['Private Notes'] || '',
          licenseNumber: contact['License Number'] || '',
          licenseState: contact['License State'] || '',
          archived: this.parseBoolean(contact['Archived']),
          loginEnabled: this.parseBoolean(contact['Login Enabled']),
          mycase_id: contact['MyCase ID']
        };

        const newContact = new Contact(contactData);
        await newContact.save();
        
        this.contactMap.set(contact['MyCase ID'], newContact._id);
        this.stats.contacts.imported++;
        
        if (this.stats.contacts.imported % 50 === 0) {
          console.log(`✓ Imported ${this.stats.contacts.imported} contacts...`);
        }
        
      } catch (error) {
        this.stats.contacts.errors++;
        if (this.stats.contacts.errors < 5) {
          console.error(`❌ Contact error: ${error.message}`);
        }
      }
    }
    
    console.log(`📊 Contacts: ${this.stats.contacts.imported} imported, ${this.stats.contacts.errors} errors`);
  }

  // STEP 3: Import All Cases
  async migrateCases() {
    console.log('\n⚖️  STEP 3: Migrating Cases...');
    const cases = await this.readCSV('/app/backup-data/cases.csv');
    
    // Get default attorney
    const defaultAttorney = await User.findOne({ role: 'admin' }) || await User.findOne();
    if (!defaultAttorney) {
      console.error('❌ No attorney found for case assignment');
      return;
    }
    
    for (const caseData of cases) {
      try {
        this.stats.cases.processed++;
        
        if (!caseData['Case/Matter Name'] && !caseData['Number']) {
          continue;
        }

        const existingCase = await Case.findOne({ mycaseId: caseData['MyCase ID'] });
        if (existingCase) {
          this.caseMap.set(caseData['MyCase ID'], existingCase._id);
          continue;
        }

        // Find attorney
        let attorney = defaultAttorney;
        const leadAttorneyName = caseData['Lead Attorney'] || caseData['Lead Attorney Name'];
        if (leadAttorneyName) {
          const foundAttorney = await User.findOne({
            $or: [
              { firstName: new RegExp(leadAttorneyName.split(' ')[0] || '', 'i') },
              { lastName: new RegExp(leadAttorneyName.split(' ').slice(-1)[0] || '', 'i') }
            ]
          });
          if (foundAttorney) attorney = foundAttorney;
        }

        const newCase = new Case({
          caseNumber: caseData['Number'] || `MIGRATED-${caseData['MyCase ID']}`,
          caseName: caseData['Case/Matter Name'] || `Case ${caseData['MyCase ID']}`,
          clientName: caseData['CONTACT NAME'] || caseData['Client Name'] || 'Unknown Client',
          clientEmail: `client${caseData['MyCase ID']}@migrated.local`,
          clientPhone: caseData['Phone No.'] || 'N/A',
          caseType: this.mapCaseType(caseData['Practice Area']),
          practiceArea: caseData['Practice Area'] || '',
          description: caseData['Case Description'] || 'Migrated case',
          status: this.parseBoolean(caseData['Case Closed']) ? 'closed' : 'active',
          attorney: attorney._id,
          openDate: this.parseDate(caseData['Open Date']) || new Date(),
          closedDate: this.parseDate(caseData['Closed Date']),
          solDate: this.parseDate(caseData['SOL Date']),
          referralSource: caseData['Where clients come from'] || '',
          flatFee: this.parseNumber(caseData['Flat fee']),
          retainer: {
            amount: this.parseNumber(caseData['Retainer']),
            received: this.parseNumber(caseData['Paid Fee']),
            balance: this.parseNumber(caseData['Outstanding Balance'])
          },
          totalBilled: this.parseNumber(caseData['Case Balance']),
          totalPaid: this.parseNumber(caseData['Earned Attorney Fee']),
          mycaseId: caseData['MyCase ID']
        });

        await newCase.save();
        this.caseMap.set(caseData['MyCase ID'], newCase._id);
        this.stats.cases.imported++;
        
        if (this.stats.cases.imported % 25 === 0) {
          console.log(`✓ Imported ${this.stats.cases.imported} cases...`);
        }
        
      } catch (error) {
        this.stats.cases.errors++;
        if (this.stats.cases.errors < 5) {
          console.error(`❌ Case error: ${error.message}`);
        }
      }
    }
    
    console.log(`📊 Cases: ${this.stats.cases.imported} imported, ${this.stats.cases.errors} errors`);
  }

  // STEP 4: Import Time Entries
  async migrateTimeEntries() {
    console.log('\n⏰ STEP 4: Migrating Time Entries...');
    const timeEntries = await this.readCSV('/app/backup-data/time_entries.csv');
    
    for (const timeEntry of timeEntries) {
      try {
        this.stats.timeEntries.processed++;
        
        // Find related case
        const caseId = this.caseMap.get(timeEntry['Case ID']) || 
                      this.caseMap.get(timeEntry['MyCase Case ID']);
        
        if (!caseId) {
          continue; // Skip if no related case found
        }

        // Find user
        const userId = this.userMap.get(timeEntry['User ID']) || 
                      this.userMap.get(timeEntry['Attorney ID']);
        
        const newTimeEntry = new TimeEntry({
          case: caseId,
          user: userId,
          date: this.parseDate(timeEntry['Date']) || new Date(),
          hours: this.parseNumber(timeEntry['Hours']),
          description: timeEntry['Description'] || 'Time entry',
          rate: this.parseNumber(timeEntry['Rate']),
          amount: this.parseNumber(timeEntry['Amount']),
          billable: this.parseBoolean(timeEntry['Billable']),
          billed: this.parseBoolean(timeEntry['Billed']),
          category: timeEntry['Category'] || 'general'
        });

        await newTimeEntry.save();
        this.stats.timeEntries.imported++;
        
      } catch (error) {
        this.stats.timeEntries.errors++;
      }
    }
    
    console.log(`📊 Time Entries: ${this.stats.timeEntries.imported} imported, ${this.stats.timeEntries.errors} errors`);
  }

  // STEP 5: Import Expenses
  async migrateExpenses() {
    console.log('\n💰 STEP 5: Migrating Expenses...');
    const expenses = await this.readCSV('/app/backup-data/expenses.csv');
    
    for (const expense of expenses) {
      try {
        this.stats.expenses.processed++;
        
        const caseId = this.caseMap.get(expense['Case ID']);
        if (!caseId) continue;

        const userId = this.userMap.get(expense['User ID']);
        
        const newExpense = new Expense({
          case: caseId,
          user: userId,
          date: this.parseDate(expense['Date']) || new Date(),
          amount: this.parseNumber(expense['Amount']),
          description: expense['Description'] || 'Expense',
          category: expense['Category'] || 'other',
          vendor: expense['Vendor'] || '',
          billable: this.parseBoolean(expense['Billable']),
          billed: this.parseBoolean(expense['Billed'])
        });

        await newExpense.save();
        this.stats.expenses.imported++;
        
      } catch (error) {
        this.stats.expenses.errors++;
      }
    }
    
    console.log(`📊 Expenses: ${this.stats.expenses.imported} imported, ${this.stats.expenses.errors} errors`);
  }

  // STEP 6: Import Leads
  async migrateLeads() {
    console.log('\n🎯 STEP 6: Migrating Leads...');
    const leads = await this.readCSV('/app/backup-data/leads.csv');
    
    for (const lead of leads) {
      try {
        this.stats.leads.processed++;
        
        const newLead = new Lead({
          firstName: lead['First Name'] || '',
          lastName: lead['Last Name'] || '',
          email: lead['Email'] || '',
          phone: lead['Phone'] || '',
          company: lead['Company'] || '',
          source: lead['Source'] || '',
          status: lead['Status'] || 'new',
          practiceArea: lead['Practice Area'] || '',
          notes: lead['Notes'] || '',
          assignedTo: this.userMap.get(lead['Assigned To']) || null
        });

        await newLead.save();
        this.stats.leads.imported++;
        
      } catch (error) {
        this.stats.leads.errors++;
      }
    }
    
    console.log(`📊 Leads: ${this.stats.leads.imported} imported, ${this.stats.leads.errors} errors`);
  }

  // Helper methods
  mapUserRole(userType) {
    if (!userType) return 'attorney';
    const type = userType.toLowerCase();
    if (type.includes('admin')) return 'admin';
    if (type.includes('paralegal')) return 'paralegal';
    if (type.includes('secretary')) return 'secretary';
    return 'attorney';
  }

  mapCaseType(practiceArea) {
    if (!practiceArea) return 'other';
    const area = practiceArea.toLowerCase();
    if (area.includes('immigration')) return 'immigration';
    if (area.includes('family')) return 'family_law';
    if (area.includes('personal injury')) return 'personal_injury';
    if (area.includes('criminal')) return 'criminal_defense';
    if (area.includes('business')) return 'business_law';
    if (area.includes('estate')) return 'estate_planning';
    return 'other';
  }

  async runFullMigration() {
    console.log('🚀 STARTING COMPREHENSIVE MIGRATION');
    console.log('=====================================');
    
    try {
      await this.connectDB();
      
      // Run migrations in dependency order
      await this.migrateUsers();
      await this.migrateContacts();
      await this.migrateCases();
      await this.migrateTimeEntries();
      await this.migrateExpenses();
      await this.migrateLeads();
      
      console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
      console.log('=====================================');
      
      // Print final statistics
      let totalImported = 0;
      let totalErrors = 0;
      
      Object.entries(this.stats).forEach(([type, stats]) => {
        console.log(`${type}: ${stats.imported} imported, ${stats.errors} errors`);
        totalImported += stats.imported;
        totalErrors += stats.errors;
      });
      
      console.log(`\n📊 TOTAL: ${totalImported} records imported, ${totalErrors} errors`);
      
      return { success: true, stats: this.stats };
      
    } catch (error) {
      console.error('💥 Migration failed:', error);
      return { success: false, error: error.message };
    } finally {
      await mongoose.disconnect();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run migration
const migration = new ComprehensiveMigration();
migration.runFullMigration();