const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

function parseDate(dateString) {
  if (!dateString || dateString === '') return null;
  
  try {
    // Handle MM/DD/YYYY format
    const cleanDate = dateString.trim();
    if (cleanDate.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
      const [month, day, year] = cleanDate.split('/');
      return new Date(year, month - 1, day);
    }
    
    // Try standard date parsing
    const parsed = new Date(cleanDate);
    if (!isNaN(parsed.getTime())) {
      return parsed;
    }
  } catch (error) {
    console.error(`Failed to parse date: ${dateString}`);
  }
  
  return null;
}

async function forceUpdateTimestamps() {
  try {
    await connectDB();
    
    console.log('🚀 Force updating timestamps with open dates...');
    
    // Read cases CSV
    const casesData = await readCSV('/app/backup-data/cases.csv');
    console.log(`📄 Found ${casesData.length} cases to process`);
    
    let casesUpdated = 0;
    let datesUpdated = 0;
    
    for (const csvCase of casesData) {
      try {
        const caseName = csvCase['Case/Matter Name'];
        if (!caseName) continue;
        
        // Find existing case in database
        const existingCase = await Case.findOne({ caseName });
        if (!existingCase) continue;
        
        // Parse open date from CSV
        const openDate = parseDate(csvCase['Open Date']);
        if (openDate) {
          // Use updateOne with timestamps: false to bypass automatic timestamp behavior
          await Case.updateOne(
            { _id: existingCase._id },
            {
              $set: {
                openDate: openDate,
                createdAt: openDate,
                updatedAt: openDate
              }
            },
            { timestamps: false }
          );
          
          casesUpdated++;
          datesUpdated++;
          
          // Show progress for first 10 updates
          if (casesUpdated <= 10) {
            console.log(`📅 Updated timestamps for "${caseName}" to ${openDate.toLocaleDateString()}`);
          }
        }
        
      } catch (error) {
        console.error(`Error processing case: ${error.message}`);
      }
    }
    
    console.log('\n🎉 TIMESTAMP UPDATE COMPLETED!');
    console.log(`✅ Updated ${casesUpdated} cases`);
    console.log(`📅 Updated ${datesUpdated} timestamps`);
    
    // Verify the updates worked
    console.log('\n🔍 Verification - Sample of updated timestamps:');
    const samples = await Case.find({}).limit(5)
      .select('caseName openDate createdAt')
      .sort({ openDate: 1 });
    
    samples.forEach((c, i) => {
      console.log(`${i+1}. Case: ${c.caseName}`);
      console.log(`   Open Date: ${c.openDate ? c.openDate.toLocaleDateString() : 'Not set'}`);
      console.log(`   Created At: ${c.createdAt ? c.createdAt.toLocaleDateString() : 'Not set'}`);
      console.log('---');
    });
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Timestamp update failed:', error);
    process.exit(1);
  }
}

forceUpdateTimestamps();