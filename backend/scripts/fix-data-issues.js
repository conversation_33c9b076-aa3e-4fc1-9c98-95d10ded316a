const mongoose = require('mongoose');
const Case = require('../models/Case');
const TimeEntry = require('../models/TimeEntry');
const Payment = require('../models/Payment');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/durian_law';

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ MongoDB disconnection error:', error);
  }
}

// Fix retainer field issues
async function fixRetainerFields() {
  console.log('\n🔧 Fixing retainer field issues...');
  
  try {
    // Find cases with problematic retainer fields
    const problematicCases = await Case.find({
      $or: [
        { 'retainer.amount': { $exists: true, $type: 'string' } },
        { 'retainer.amount': { $exists: true, $type: 'object' } },
        { 'retainer.received': { $exists: true, $type: 'string' } },
        { 'retainer.balance': { $exists: true, $type: 'string' } }
      ]
    });

    console.log(`Found ${problematicCases.length} cases with problematic retainer fields`);

    for (const caseDoc of problematicCases) {
      try {
        // Fix retainer fields
        if (caseDoc.retainer) {
          // Ensure retainer.amount is a number
          if (typeof caseDoc.retainer.amount === 'string') {
            caseDoc.retainer.amount = parseFloat(caseDoc.retainer.amount) || 0;
          } else if (typeof caseDoc.retainer.amount === 'object') {
            caseDoc.retainer.amount = 0;
          }

          // Ensure retainer.received is a number
          if (typeof caseDoc.retainer.received === 'string') {
            caseDoc.retainer.received = parseFloat(caseDoc.retainer.received) || 0;
          } else if (typeof caseDoc.retainer.received === 'object') {
            caseDoc.retainer.received = 0;
          }

          // Ensure retainer.balance is a number
          if (typeof caseDoc.retainer.balance === 'string') {
            caseDoc.retainer.balance = parseFloat(caseDoc.retainer.balance) || 0;
          } else if (typeof caseDoc.retainer.balance === 'object') {
            caseDoc.retainer.balance = 0;
          }

          // Calculate balance if not set
          if (caseDoc.retainer.balance === 0 && caseDoc.retainer.amount > 0) {
            caseDoc.retainer.balance = caseDoc.retainer.amount - (caseDoc.totalPaid || 0);
          }
        }

        // Ensure totalPaid is a number
        if (typeof caseDoc.totalPaid === 'string') {
          caseDoc.totalPaid = parseFloat(caseDoc.totalPaid) || 0;
        }

        // Ensure totalBilled is a number
        if (typeof caseDoc.totalBilled === 'string') {
          caseDoc.totalBilled = parseFloat(caseDoc.totalBilled) || 0;
        }

        await caseDoc.save();
        console.log(`✅ Fixed case: ${caseDoc.caseNumber} - ${caseDoc.caseName}`);
      } catch (error) {
        console.error(`❌ Error fixing case ${caseDoc.caseNumber}:`, error.message);
      }
    }

    console.log('✅ Retainer field fixes completed');
  } catch (error) {
    console.error('❌ Error fixing retainer fields:', error);
  }
}

// Fix TimeEntry populate issues
async function fixTimeEntryPopulate() {
  console.log('\n🔧 Fixing TimeEntry populate issues...');
  
  try {
    // Check for TimeEntries with invalid caseId references
    const timeEntries = await TimeEntry.find({}).populate('caseId');
    
    let fixedCount = 0;
    for (const entry of timeEntries) {
      if (!entry.caseId) {
        console.log(`⚠️ TimeEntry ${entry._id} has invalid caseId: ${entry.caseId}`);
        // You might want to delete these or mark them as invalid
        // await TimeEntry.findByIdAndDelete(entry._id);
        // fixedCount++;
      }
    }

    console.log(`✅ TimeEntry populate check completed. Found ${fixedCount} invalid entries`);
  } catch (error) {
    console.error('❌ Error checking TimeEntry populate:', error);
  }
}

// Fix Payment populate issues
async function fixPaymentPopulate() {
  console.log('\n🔧 Fixing Payment populate issues...');
  
  try {
    // Check for Payments with invalid caseId references
    const payments = await Payment.find({}).populate('caseId');
    
    let fixedCount = 0;
    for (const payment of payments) {
      if (!payment.caseId) {
        console.log(`⚠️ Payment ${payment._id} has invalid caseId: ${payment.caseId}`);
        // You might want to delete these or mark them as invalid
        // await Payment.findByIdAndDelete(payment._id);
        // fixedCount++;
      }
    }

    console.log(`✅ Payment populate check completed. Found ${fixedCount} invalid entries`);
  } catch (error) {
    console.error('❌ Error checking Payment populate:', error);
  }
}

// Recalculate case totals
async function recalculateCaseTotals() {
  console.log('\n🔧 Recalculating case totals...');
  
  try {
    const cases = await Case.find({});
    let updatedCount = 0;

    for (const caseDoc of cases) {
      try {
        // Calculate total paid from payments
        const totalPaid = await Payment.aggregate([
          { $match: { caseId: caseDoc._id, status: { $ne: 'deleted' } } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]);

        const newTotalPaid = totalPaid[0]?.total || 0;

        // Calculate total billed from time entries
        const totalBilled = await TimeEntry.aggregate([
          { $match: { caseId: caseDoc._id, nonbillable: false } },
          { $group: { _id: null, total: { $sum: '$total' } } }
        ]);

        const newTotalBilled = totalBilled[0]?.total || 0;

        // Update case totals
        let needsUpdate = false;
        
        if (caseDoc.totalPaid !== newTotalPaid) {
          caseDoc.totalPaid = newTotalPaid;
          needsUpdate = true;
        }

        if (caseDoc.totalBilled !== newTotalBilled) {
          caseDoc.totalBilled = newTotalBilled;
          needsUpdate = true;
        }

        // Update retainer balance
        if (caseDoc.retainer && caseDoc.retainer.amount > 0) {
          const newRetainerBalance = Math.max(0, caseDoc.retainer.amount - newTotalPaid);
          if (caseDoc.retainer.balance !== newRetainerBalance) {
            caseDoc.retainer.balance = newRetainerBalance;
            needsUpdate = true;
          }
        }

        if (needsUpdate) {
          await caseDoc.save();
          updatedCount++;
          console.log(`✅ Updated case totals: ${caseDoc.caseNumber} - ${caseDoc.caseName}`);
        }
      } catch (error) {
        console.error(`❌ Error updating case ${caseDoc.caseNumber}:`, error.message);
      }
    }

    console.log(`✅ Case totals recalculation completed. Updated ${updatedCount} cases`);
  } catch (error) {
    console.error('❌ Error recalculating case totals:', error);
  }
}

// Validate data integrity
async function validateDataIntegrity() {
  console.log('\n🔍 Validating data integrity...');
  
  try {
    // Check Case model
    const caseCount = await Case.countDocuments();
    console.log(`📊 Cases: ${caseCount}`);

    // Check TimeEntry model
    const timeEntryCount = await TimeEntry.countDocuments();
    console.log(`📊 TimeEntries: ${timeEntryCount}`);

    // Check Payment model
    const paymentCount = await Payment.countDocuments();
    console.log(`📊 Payments: ${paymentCount}`);

    // Check for orphaned records
    const orphanedTimeEntries = await TimeEntry.countDocuments({
      caseId: { $exists: false }
    });
    console.log(`⚠️ Orphaned TimeEntries: ${orphanedTimeEntries}`);

    const orphanedPayments = await Payment.countDocuments({
      caseId: { $exists: false }
    });
    console.log(`⚠️ Orphaned Payments: ${orphanedPayments}`);

    // Check for invalid references
    const invalidTimeEntryRefs = await TimeEntry.countDocuments({
      caseId: { $exists: true, $ne: null }
    });
    console.log(`📊 TimeEntries with valid caseId: ${invalidTimeEntryRefs}`);

    const invalidPaymentRefs = await Payment.countDocuments({
      caseId: { $exists: true, $ne: null }
    });
    console.log(`📊 Payments with valid caseId: ${invalidPaymentRefs}`);

    console.log('✅ Data integrity validation completed');
  } catch (error) {
    console.error('❌ Error validating data integrity:', error);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting data fix script...');
  
  try {
    await connectDB();
    
    // Run all fixes
    await fixRetainerFields();
    await fixTimeEntryPopulate();
    await fixPaymentPopulate();
    await recalculateCaseTotals();
    await validateDataIntegrity();
    
    console.log('\n✨ All fixes completed successfully!');
  } catch (error) {
    console.error('💥 Script failed:', error);
  } finally {
    await disconnectDB();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  fixRetainerFields,
  fixTimeEntryPopulate,
  fixPaymentPopulate,
  recalculateCaseTotals,
  validateDataIntegrity
};
