const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

async function initializeDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/mycase');
    console.log('Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      console.log('Admin user already exists, skipping user creation');
    } else {
      // Create admin user
      const adminUser = new User({
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        isActive: true
      });

      await adminUser.save();
      console.log('Admin user created successfully');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
    }

    // Create additional test users
    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'attorney123',
        firstName: '<PERSON>',
        lastName: 'Attorney',
        role: 'attorney',
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'manager123',
        firstName: 'Sarah',
        lastName: 'Manager',
        role: 'case_manager',
        isActive: true
      }
    ];

    for (const userData of testUsers) {
      const existingUser = await User.findOne({ email: userData.email });
      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        console.log(`Created user: ${userData.email} (${userData.role})`);
      } else {
        console.log(`User ${userData.email} already exists`);
      }
    }

    console.log('Database initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  }
}

initializeDatabase();
