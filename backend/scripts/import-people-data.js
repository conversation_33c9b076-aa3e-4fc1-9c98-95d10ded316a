const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

function findBestCaseMatch(personName, caseArray) {
  if (!personName) return [];
  
  const searchLower = personName.toLowerCase();
  const matches = [];
  
  for (const caseItem of caseArray) {
    const caseNameLower = caseItem.caseName.toLowerCase();
    const clientNameLower = caseItem.clientName.toLowerCase();
    
    // Check if person name appears in case name or client name
    if (caseNameLower.includes(searchLower) || 
        clientNameLower.includes(searchLower) ||
        searchLower.includes(clientNameLower) ||
        searchLower.includes(caseNameLower.split('_')[0]?.toLowerCase() || '')) {
      matches.push(caseItem);
    }
  }
  
  return matches;
}

async function importPeopleData() {
  try {
    await connectDB();
    
    console.log('🚀 Starting people data import...');
    
    // Check if people.csv exists
    if (!fs.existsSync('/app/backup-data/people.csv')) {
      console.log('❌ people.csv not found in backup-data directory');
      await mongoose.connection.close();
      return;
    }
    
    // Read people CSV
    const peopleData = await readCSV('/app/backup-data/people.csv');
    console.log(`📄 Found ${peopleData.length} people records to process`);
    
    // Show CSV structure
    if (peopleData.length > 0) {
      console.log('\n📋 People CSV columns:');
      Object.keys(peopleData[0]).forEach((key, index) => {
        console.log(`   ${index + 1}. ${key}`);
      });
    }
    
    // Get all cases for matching
    const cases = await Case.find({}, { _id: 1, caseName: 1, clientName: 1 });
    console.log(`📊 Found ${cases.length} cases for people matching`);
    
    let peopleProcessed = 0;
    let contactsUpdated = 0;
    
    for (const person of peopleData) {
      try {
        const personName = person['Name'] || person['Full Name'] || person['Client Name'] || '';
        const firstName = person['First Name'] || '';
        const lastName = person['Last Name'] || '';
        const email = person['Email'] || person['Email Address'] || '';
        const phone = person['Phone'] || person['Phone Number'] || '';
        const jobTitle = person['Job Title'] || person['Title'] || '';
        const homeAddress = person['Home Address'] || person['Address'] || '';
        const specialNotes = person['Special Notes'] || person['Notes'] || '';
        
        if (!personName && !firstName && !lastName) {
          continue;
        }
        
        const fullName = personName || `${firstName} ${lastName}`.trim();
        
        // Find matching cases
        const matchingCases = findBestCaseMatch(fullName, cases);
        
        if (matchingCases.length > 0) {
          // Create contact information object
          const contactInfo = {
            name: fullName,
            firstName: firstName,
            lastName: lastName,
            email: email,
            phone: phone,
            jobTitle: jobTitle,
            homeAddress: homeAddress,
            specialNotes: specialNotes,
            type: 'client'
          };
          
          // Update each matching case
          for (const matchingCase of matchingCases) {
            await Case.findByIdAndUpdate(matchingCase._id, {
              clientName: fullName,
              clientEmail: email || `${fullName.toLowerCase().replace(/\s+/g, '.')}@client.local`,
              clientPhone: phone || 'N/A',
              $push: {
                contactsList: contactInfo
              }
            });
            
            contactsUpdated++;
            
            if (contactsUpdated <= 10) {
              console.log(`👤 Added contact "${fullName}" to case "${matchingCase.caseName}"`);
              console.log(`   Email: ${email || 'N/A'}`);
              console.log(`   Phone: ${phone || 'N/A'}`);
              console.log(`   Job Title: ${jobTitle || 'N/A'}`);
              console.log('---');
            }
          }
        }
        
        peopleProcessed++;
        
      } catch (error) {
        console.error(`Error processing person: ${error.message}`);
      }
    }
    
    console.log('\n🎉 PEOPLE DATA IMPORT COMPLETED!');
    console.log(`✅ Processed ${peopleProcessed} people records`);
    console.log(`📞 Updated ${contactsUpdated} case contacts`);
    
    // Show sample of updated data
    console.log('\n📊 Sample of updated case contacts:');
    const samples = await Case.find({ contactsList: { $exists: true, $ne: [] } })
      .limit(3)
      .select('caseName clientName clientEmail clientPhone contactsList');
    
    samples.forEach((c, i) => {
      console.log(`${i+1}. Case: ${c.caseName}`);
      console.log(`   Client: ${c.clientName}`);
      console.log(`   Email: ${c.clientEmail}`);
      console.log(`   Phone: ${c.clientPhone}`);
      console.log(`   Contacts: ${c.contactsList?.length || 0} contact(s)`);
      if (c.contactsList && c.contactsList.length > 0) {
        console.log(`   First Contact: ${c.contactsList[0].name} (${c.contactsList[0].email || 'No email'})`);
      }
      console.log('---');
    });
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 People data import failed:', error);
    process.exit(1);
  }
}

importPeopleData();