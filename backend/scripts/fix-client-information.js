const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

function extractClientInfo(contactsField, caseName) {
  const contacts = contactsField || '';
  const lines = contacts.split('\n').map(line => line.trim()).filter(line => line);
  
  let clientName = null;
  let clientEmail = null;
  let clientPhone = null;
  let attorney = null;
  let caseManager = null;
  
  // Extract client name from case name if it contains recognizable patterns
  if (caseName) {
    // Pattern: "FirstName LastName_" or "FirstName LastName " 
    const namePatterns = [
      /([A-Z][a-z]+ [A-Z][a-z]+)_/,  // Before underscore
      /([A-Z][a-z]+ [A-Z][a-z]+)\s/,  // Before space
      /_([A-Z][a-z]+ [A-Z][a-z]+)/,   // After underscore
      /([A-Z][a-z]+, [A-Z][a-z]+)/    // Last, First format
    ];
    
    for (const pattern of namePatterns) {
      const match = caseName.match(pattern);
      if (match) {
        let name = match[1].trim();
        // Handle "Last, First" format
        if (name.includes(',')) {
          const parts = name.split(',').map(p => p.trim());
          name = `${parts[1]} ${parts[0]}`;
        }
        clientName = name;
        break;
      }
    }
  }
  
  // Parse contacts for detailed information
  for (const line of lines) {
    // Look for client designation
    if (line.toLowerCase().includes('(client)')) {
      const nameMatch = line.replace(/\s*\(client\)\s*/i, '').trim();
      if (nameMatch && nameMatch.length > 2) {
        clientName = nameMatch;
      }
    }
    
    // Look for attorney
    if (line.toLowerCase().includes('(attorney)')) {
      attorney = line.replace(/\s*\(attorney\)\s*/i, '').trim();
    }
    
    // Look for case manager/office manager
    if (line.toLowerCase().includes('(office manager)') || line.toLowerCase().includes('(case manager)')) {
      caseManager = line.replace(/\s*\(office manager\)\s*/i, '').replace(/\s*\(case manager\)\s*/i, '').trim();
    }
    
    // Extract email (basic pattern)
    const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (emailMatch && !clientEmail) {
      clientEmail = emailMatch[1];
    }
    
    // Extract phone (basic patterns)
    const phonePatterns = [
      /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/,  // (************* or ************
      /\d{3}[-.\s]\d{3}[-.\s]\d{4}/,         // ************
      /\+1[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/ // ****** 456 7890
    ];
    
    for (const pattern of phonePatterns) {
      const phoneMatch = line.match(pattern);
      if (phoneMatch && !clientPhone) {
        clientPhone = phoneMatch[0].trim();
        break;
      }
    }
  }
  
  // If no explicit client found in contacts, try to extract from case name
  if (!clientName && caseName) {
    // Try to extract client name from more complex patterns
    const complexPatterns = [
      // "Company Name_Person Name" format
      /^([^_]+)_([A-Z][a-z]+ [A-Z][a-z]+)/,
      // "Person Name_Description" format  
      /^([A-Z][a-z]+ [A-Z][a-z]+)_/,
      // Just find any proper name
      /([A-Z][a-z]+ [A-Z][a-z]+)/
    ];
    
    for (const pattern of complexPatterns) {
      const match = caseName.match(pattern);
      if (match) {
        // Take the most name-like match
        clientName = match[1] || match[2];
        if (clientName && clientName.length > 5) {
          break;
        }
      }
    }
  }
  
  // Generate a proper email if none found
  if (!clientEmail && clientName) {
    const emailName = clientName.toLowerCase()
      .replace(/[^a-z\s]/g, '')
      .replace(/\s+/g, '.');
    clientEmail = `${emailName}@client.local`;
  }
  
  return {
    clientName: clientName || 'Unknown Client',
    clientEmail: clientEmail || '<EMAIL>',
    clientPhone: clientPhone || 'N/A',
    attorney,
    caseManager
  };
}

async function fixClientInformation() {
  try {
    await connectDB();
    
    console.log('🚀 Starting client information fix...');
    
    // Get user mappings for attorney assignments
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`👥 Found ${users.length} users for attorney/case manager mapping`);
    
    // Read cases CSV
    const casesData = await readCSV('/app/backup-data/cases.csv');
    console.log(`📄 Found ${casesData.length} cases to process for client information`);
    
    let casesUpdated = 0;
    let clientInfoExtracted = 0;
    
    for (const csvCase of casesData) {
      try {
        const caseName = csvCase['Case/Matter Name'];
        if (!caseName) continue;
        
        // Find existing case in database
        const existingCase = await Case.findOne({ caseName });
        if (!existingCase) continue;
        
        // Extract client information
        const clientInfo = extractClientInfo(csvCase['Contacts'], caseName);
        
        // Find attorney and case manager IDs
        let attorneyId = existingCase.attorney; // Keep existing if no match found
        let caseManagerId = existingCase.caseManager;
        
        if (clientInfo.attorney && userMap.has(clientInfo.attorney)) {
          attorneyId = userMap.get(clientInfo.attorney);
        }
        
        if (clientInfo.caseManager && userMap.has(clientInfo.caseManager)) {
          caseManagerId = userMap.get(clientInfo.caseManager);
        }
        
        // Update case with extracted information
        const updateData = {
          clientName: clientInfo.clientName,
          clientEmail: clientInfo.clientEmail,
          clientPhone: clientInfo.clientPhone,
          attorney: attorneyId
        };
        
        if (caseManagerId) {
          updateData.caseManager = caseManagerId;
        }
        
        await Case.findByIdAndUpdate(existingCase._id, updateData);
        
        casesUpdated++;
        if (clientInfo.clientName !== 'Unknown Client') {
          clientInfoExtracted++;
        }
        
        // Show progress for first 10 updates
        if (casesUpdated <= 10) {
          console.log(`🔄 Updated "${caseName}"`);
          console.log(`   Client: ${clientInfo.clientName}`);
          console.log(`   Email: ${clientInfo.clientEmail}`);
          console.log(`   Phone: ${clientInfo.clientPhone}`);
          console.log(`   Attorney: ${clientInfo.attorney || 'Not specified'}`);
          console.log('---');
        }
        
      } catch (error) {
        console.error(`Error processing case: ${error.message}`);
      }
    }
    
    console.log('\n🎉 CLIENT INFORMATION UPDATE COMPLETED!');
    console.log(`✅ Updated ${casesUpdated} cases with client information`);
    console.log(`📧 Extracted proper client info for ${clientInfoExtracted} cases`);
    
    // Show sample of updated data
    console.log('\n📊 Sample of updated client information:');
    const samples = await Case.find({}).limit(5).select('caseName clientName clientEmail clientPhone attorney caseManager').populate('attorney', 'firstName lastName').populate('caseManager', 'firstName lastName');
    
    samples.forEach((c, i) => {
      console.log(`${i+1}. Case: ${c.caseName}`);
      console.log(`   Client: ${c.clientName}`);
      console.log(`   Email: ${c.clientEmail}`);
      console.log(`   Phone: ${c.clientPhone}`);
      console.log(`   Attorney: ${c.attorney ? `${c.attorney.firstName} ${c.attorney.lastName}` : 'Not assigned'}`);
      console.log(`   Case Manager: ${c.caseManager ? `${c.caseManager.firstName} ${c.caseManager.lastName}` : 'Not assigned'}`);
      console.log('---');
    });
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Client information fix failed:', error);
    process.exit(1);
  }
}

fixClientInformation();