const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Document = require('../models/Document');
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseDate(dateString) {
  if (!dateString) return null;
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

function parseNumber(numberString) {
  if (!numberString) return 0;
  return parseFloat(numberString.toString().replace(/[^\\d.-]/g, '')) || 0;
}

function getDocumentType(fileName, description) {
  const name = (fileName || '').toLowerCase();
  const desc = (description || '').toLowerCase();
  
  if (name.includes('contract') || desc.includes('contract') || desc.includes('agreement')) {
    return 'contract';
  }
  if (name.includes('correspondence') || desc.includes('letter') || desc.includes('email')) {
    return 'correspondence';
  }
  if (name.includes('filing') || desc.includes('court') || desc.includes('motion')) {
    return 'filing';
  }
  if (name.includes('evidence') || desc.includes('exhibit') || desc.includes('proof')) {
    return 'evidence';
  }
  return 'other';
}

function getMimeType(fileName) {
  if (!fileName) return 'application/octet-stream';
  
  const ext = fileName.toLowerCase().split('.').pop();
  const mimeTypes = {
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    txt: 'text/plain',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function migrateDocuments() {
  try {
    await connectDB();
    
    console.log('🚀 Starting documents migration...');
    
    // Build case and user mappings
    console.log('📋 Building case and user mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${cases.length} cases and ${users.length} users for mapping`);
    
    // Read documents CSV
    const documents = await readCSV('/app/backup-data/documents.csv');
    console.log(`📄 Found ${documents.length} document records to process`);
    
    let imported = 0;
    let errors = 0;
    let skipped = 0;
    
    for (const docData of documents) {
      try {
        // Find matching case
        const caseName = docData['Case Name'] || '';
        const caseId = caseMap.get(caseName);
        
        if (!caseId) {
          skipped++;
          continue;
        }
        
        // Find uploading user
        const uploadedByName = docData['Uploaded By'] || docData['Created By'] || '';
        const uploadedBy = userMap.get(uploadedByName);
        
        if (!uploadedBy) {
          skipped++;
          continue;
        }
        
        // Parse document data
        const fileName = docData['File Name'] || docData['Name'] || 'Unknown Document';
        const description = docData['Description'] || docData['Notes'] || '';
        const fileSize = parseNumber(docData['File Size']) || 0;
        const uploadDate = parseDate(docData['Upload Date']) || parseDate(docData['Created At']) || new Date();
        
        // Create document
        const newDocument = new Document({
          name: fileName,
          description,
          type: getDocumentType(fileName, description),
          fileSize,
          fileName,
          filePath: docData['File Path'] || '',
          mimeType: getMimeType(fileName),
          caseId,
          uploadedBy,
          uploadDate,
          tags: docData['Tags'] ? docData['Tags'].split(',').map(tag => tag.trim()) : [],
          mycase_id: docData['ID'] || `doc_${imported + 1}`
        });
        
        await newDocument.save();
        imported++;
        
        if (imported % 500 === 0) {
          console.log(`📊 Processed ${imported} documents...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing document:`, error.message);
        errors++;
      }
    }
    
    console.log('\\n🎉 DOCUMENTS MIGRATION COMPLETED!');
    console.log(`✅ Successfully imported: ${imported} documents`);
    console.log(`⚠️ Skipped: ${skipped} documents`);
    console.log(`❌ Errors: ${errors} documents`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateDocuments();