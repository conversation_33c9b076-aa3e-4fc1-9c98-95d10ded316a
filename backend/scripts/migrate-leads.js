const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Lead = require('../models/Lead');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseDate(dateString) {
  if (!dateString) return null;
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

function getCaseTypeFromPracticeArea(practiceArea) {
  if (!practiceArea) return 'other';
  const area = practiceArea.toLowerCase();
  
  if (area.includes('immigration')) return 'immigration';
  if (area.includes('family')) return 'family_law';
  if (area.includes('criminal')) return 'criminal_defense';
  if (area.includes('personal injury') || area.includes('pi')) return 'personal_injury';
  if (area.includes('business')) return 'business_law';
  if (area.includes('estate')) return 'estate_planning';
  
  return 'other';
}

function getLeadSource(source) {
  if (!source) return 'other';
  const sourceLower = source.toLowerCase();
  
  if (sourceLower.includes('google') || sourceLower.includes('website')) return 'website';
  if (sourceLower.includes('referral') || sourceLower.includes('referred')) return 'referral';
  if (sourceLower.includes('advertising') || sourceLower.includes('ad')) return 'advertising';
  if (sourceLower.includes('social') || sourceLower.includes('facebook') || sourceLower.includes('instagram')) return 'social_media';
  
  return 'other';
}

function getLeadStatus(status, convertedDate) {
  if (!status) return 'new';
  
  if (convertedDate) return 'converted';
  
  const statusLower = status.toLowerCase();
  if (statusLower.includes('contacted')) return 'contacted';
  if (statusLower.includes('qualified')) return 'qualified';
  if (statusLower.includes('closed')) return 'closed';
  
  return 'new';
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function migrateLeads() {
  try {
    await connectDB();
    
    console.log('🚀 Starting leads migration...');
    
    // Build user mappings
    console.log('📋 Building user mappings...');
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${users.length} users for mapping`);
    
    // Read leads CSV
    const leads = await readCSV('/app/backup-data/leads.csv');
    console.log(`📄 Found ${leads.length} lead records to process`);
    
    let imported = 0;
    let errors = 0;
    let skipped = 0;
    
    for (const leadData of leads) {
      try {
        // Build full name
        const firstName = leadData['First Name'] || '';
        const middleName = leadData['Middle Name'] || '';
        const lastName = leadData['Last Name'] || '';
        const fullName = [firstName, middleName, lastName].filter(n => n).join(' ').trim();
        
        if (!fullName) {
          skipped++;
          continue;
        }
        
        // Check if lead already exists
        const existingLead = await Lead.findOne({
          $or: [
            { 'contact.email': leadData['Lead Contact Email'] },
            { name: fullName }
          ]
        });
        
        if (existingLead) {
          skipped++;
          continue;
        }
        
        // Find assigned user
        const createdBy = userMap.get(leadData['Created By']) || userMap.get('System Administrator');
        const assignedTo = userMap.get(leadData['Assign To']) || createdBy;
        
        // Build address
        const address = {
          street: leadData['Street'],
          street2: leadData['Street 2'],
          city: leadData['City'],
          state: leadData['State'],
          postalCode: leadData['Zip Code'],
          country: leadData['Country'] || 'USA'
        };
        
        // Build contact info
        const contact = {
          email: leadData['Lead Contact Email'],
          workPhone: leadData['Lead Contact Work #'],
          homePhone: leadData['Lead Contact Home #'],
          cellPhone: leadData['Lead Contact Cell #']
        };
        
        // Create lead
        const newLead = new Lead({
          name: fullName,
          firstName,
          middleName,
          lastName,
          contact,
          address,
          practiceArea: leadData['Practice Area'],
          caseType: getCaseTypeFromPracticeArea(leadData['Practice Area']),
          status: getLeadStatus(leadData['Current Status'], leadData['Converted Date']),
          source: getLeadSource(leadData['Lead Source']),
          details: leadData['Details'],
          value: parseFloat(leadData['Value']) || 0,
          referredBy: leadData['Lead Referred By'],
          birthday: parseDate(leadData['Birthday']),
          licenseNumber: leadData['License Number'],
          licenseState: leadData['License State'],
          notes: leadData['Notes'],
          addedDate: parseDate(leadData['Added Date']) || new Date(),
          convertedDate: parseDate(leadData['Converted Date']),
          potentialCaseName: leadData['Potential Case Name'],
          potentialCaseDescription: leadData['Potential Case Description'],
          conflictCheck: leadData['Conflict Check?'] === 'Yes',
          conflictCheckNotes: leadData['Conflict Check Notes'],
          assignedTo,
          createdBy,
          mycase_id: `lead_${imported + 1}` // Generate unique ID
        });
        
        await newLead.save();
        imported++;
        
        if (imported % 100 === 0) {
          console.log(`📊 Processed ${imported} leads...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing lead:`, error.message);
        errors++;
      }
    }
    
    console.log('\n🎉 LEADS MIGRATION COMPLETED!');
    console.log(`✅ Successfully imported: ${imported} leads`);
    console.log(`⚠️ Skipped: ${skipped} leads`);
    console.log(`❌ Errors: ${errors} leads`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateLeads();