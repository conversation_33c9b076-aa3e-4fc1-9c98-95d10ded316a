const mongoose = require('mongoose');
const fs = require('fs');
const csv = require('csv-parser');
const Contact = require('../models/Contact');
const User = require('../models/User');

async function connectDB() {
  await mongoose.connect(process.env.MONGODB_URI || 'mongodb://mongo:27017/durian_law');
  console.log('Connected to MongoDB');
}

async function importContacts() {
  console.log('Starting contacts import...');
  const contacts = [];
  
  return new Promise((resolve, reject) => {
    fs.createReadStream('/app/backup-data/people.csv')
      .pipe(csv())
      .on('data', (data) => {
        contacts.push(data);
      })
      .on('end', async () => {
        console.log(`Found ${contacts.length} contacts to import`);
        
        let imported = 0;
        for (const contactData of contacts.slice(0, 10)) { // Import first 10 for testing
          try {
            if (!contactData['First Name'] && !contactData['Last Name'] && !contactData['Company']) {
              continue;
            }
            
            const newContact = new Contact({
              firstName: contactData['First Name'] || '',
              lastName: contactData['Last Name'] || '',
              middleName: contactData['Middle Name'] || '',
              company: contactData['Company'] || '',
              jobTitle: contactData['Job Title'] || '',
              email: contactData['E-mail Address'] || '',
              phones: {
                home: contactData['Home Phone'] || '',
                work: contactData['Work Phone'] || '',
                mobile: contactData['Mobile Phone'] || '',
                fax: contactData['Home Fax'] || ''
              },
              homeAddress: {
                street: contactData['Home Street'] || '',
                street2: contactData['Home Street 2'] || '',
                city: contactData['Home City'] || '',
                state: contactData['Home State'] || '',
                postalCode: contactData['Home Postal Code'] || '',
                country: contactData['Home Country/Region'] || 'USA'
              },
              webPage: contactData['Web Page'] || '',
              contactGroup: contactData['Contact Group'] || '',
              birthday: contactData['Birthday'] ? new Date(contactData['Birthday']) : null,
              privateNotes: contactData['Private Notes'] || '',
              licenseNumber: contactData['License Number'] || '',
              licenseState: contactData['License State'] || '',
              archived: contactData['Archived'] === 'true',
              loginEnabled: contactData['Login Enabled'] === 'true',
              mycase_id: contactData['MyCase ID']
            });
            
            await newContact.save();
            imported++;
            console.log(`Imported: ${newContact.fullName}`);
            
          } catch (error) {
            console.error(`Error importing contact: ${error.message}`);
          }
        }
        
        console.log(`Imported ${imported} contacts successfully`);
        resolve();
      })
      .on('error', reject);
  });
}

async function run() {
  try {
    await connectDB();
    await importContacts();
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

run();