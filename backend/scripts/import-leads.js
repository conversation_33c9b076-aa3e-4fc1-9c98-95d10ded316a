const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Lead = require('../models/Lead');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

function parseDate(dateString) {
  if (!dateString || dateString === '') return null;
  
  try {
    // Handle MM/DD/YYYY format
    const cleanDate = dateString.trim();
    if (cleanDate.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
      const [month, day, year] = cleanDate.split('/');
      return new Date(year, month - 1, day);
    }
    
    // Try standard date parsing
    const parsed = new Date(cleanDate);
    if (!isNaN(parsed.getTime())) {
      return parsed;
    }
  } catch (error) {
    console.error(`Failed to parse date: ${dateString}`);
  }
  
  return null;
}

function parseNumber(numberString) {
  if (!numberString) return 0;
  const cleaned = numberString.toString().replace(/[^0-9.-]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
}

function mapCaseType(practiceArea) {
  if (!practiceArea) return 'other';
  
  const area = practiceArea.toLowerCase();
  
  if (area.includes('immigration')) return 'immigration';
  if (area.includes('family') && area.includes('tx')) return 'family_law_tx';
  if (area.includes('family') && area.includes('il')) return 'family_law_il';
  if (area.includes('family')) return 'family_law';
  if (area.includes('criminal')) return 'criminal_defense';
  if (area.includes('personal injury')) return 'personal_injury';
  if (area.includes('estate')) return 'estate_planning';
  if (area.includes('business')) return 'business_law';
  if (area.includes('civil')) return 'civil_litigation';
  if (area.includes('employment')) return 'employment_law';
  if (area.includes('real estate')) return 'real_estate';
  
  return 'other';
}

function findBestUserMatch(name, userMap) {
  if (!name) return null;
  
  // Direct match
  if (userMap.has(name)) {
    return userMap.get(name);
  }
  
  const searchLower = name.toLowerCase();
  
  // Partial matching
  for (const [userName, userId] of userMap.entries()) {
    const userLower = userName.toLowerCase();
    if (userLower.includes(searchLower) || searchLower.includes(userLower)) {
      return userId;
    }
  }
  
  return null;
}

async function importLeads() {
  try {
    await connectDB();
    
    console.log('🚀 Starting leads import...');
    
    // Check if leads.csv exists
    if (!fs.existsSync('/app/backup-data/leads.csv')) {
      console.log('❌ leads.csv not found in backup-data directory');
      await mongoose.connection.close();
      return;
    }
    
    // Get user mappings
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`👥 Found ${users.length} users for assignment mapping`);
    
    // Read leads CSV
    const leadsData = await readCSV('/app/backup-data/leads.csv');
    console.log(`📄 Found ${leadsData.length} leads to import`);
    
    // Show CSV structure
    if (leadsData.length > 0) {
      console.log('\n📋 Leads CSV columns:');
      Object.keys(leadsData[0]).forEach((key, index) => {
        console.log(`   ${index + 1}. ${key}`);
      });
    }
    
    let leadsImported = 0;
    let leadsAssigned = 0;
    
    for (const leadData of leadsData) {
      try {
        // Extract basic information
        const firstName = leadData['First Name'] || '';
        const middleName = leadData['Middle Name'] || '';
        const lastName = leadData['Last Name'] || '';
        const fullName = [firstName, middleName, lastName].filter(n => n && n.trim()).join(' ').trim();
        
        if (!fullName || fullName.length < 2) continue;
        
        // Extract contact information
        const email = leadData['E-mail Address'] || leadData['Email'] || '';
        const homePhone = leadData['Home Phone'] || '';
        const workPhone = leadData['Work Phone'] || '';
        const cellPhone = leadData['Mobile Phone'] || leadData['Cell Phone'] || '';
        
        // Extract address
        const street = leadData['Home Street'] || '';
        const city = leadData['Home City'] || '';
        const state = leadData['Home State'] || '';
        const postalCode = leadData['Home Postal Code'] || '';
        
        // Extract lead details
        const practiceArea = leadData['Practice Area'] || '';
        const caseType = mapCaseType(practiceArea);
        const notes = leadData['Private Notes'] || leadData['Notes'] || '';
        const company = leadData['Company'] || '';
        const jobTitle = leadData['Job Title'] || '';
        
        // Parse dates
        const addedDate = parseDate(leadData['Created Date']) || new Date();
        const birthday = parseDate(leadData['Birthday']);
        
        // Parse value
        const value = parseNumber(leadData['Value'] || leadData['Potential Value'] || 0);
        
        // Find assigned user
        const assignedToName = leadData['Assigned To'] || leadData['Created By'] || '';
        const assignedToId = findBestUserMatch(assignedToName, userMap);
        
        // Create lead object
        const leadObj = {
          name: fullName,
          firstName,
          middleName,
          lastName,
          contact: {
            email,
            homePhone,
            workPhone,
            cellPhone
          },
          address: {
            street,
            city,
            state,
            postalCode,
            country: 'USA'
          },
          practiceArea,
          caseType,
          status: 'new',
          source: 'other',
          details: notes,
          value,
          addedDate,
          birthday,
          assignedTo: assignedToId,
          createdBy: assignedToId,
          notes,
          mycase_id: leadData['MyCase ID'] || '',
          priority: 0
        };
        
        // Create lead in database
        const newLead = new Lead(leadObj);
        await newLead.save();
        
        leadsImported++;
        if (assignedToId) leadsAssigned++;
        
        // Show progress for first 10 imports
        if (leadsImported <= 10) {
          console.log(`📋 Imported lead: ${fullName}`);
          console.log(`   Email: ${email || 'N/A'}`);
          console.log(`   Phone: ${cellPhone || homePhone || workPhone || 'N/A'}`);
          console.log(`   Practice Area: ${practiceArea || 'N/A'}`);
          console.log(`   Assigned To: ${assignedToName || 'Unassigned'}`);
          console.log(`   Value: $${value}`);
          console.log('---');
        }
        
      } catch (error) {
        console.error(`Error importing lead: ${error.message}`);
      }
    }
    
    console.log('\n🎉 LEADS IMPORT COMPLETED!');
    console.log(`✅ Imported ${leadsImported} leads`);
    console.log(`👥 Assigned ${leadsAssigned} leads to staff members`);
    
    // Show summary statistics
    const statusSummary = await Lead.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    console.log('\n📊 Lead Status Summary:');
    statusSummary.forEach(status => {
      console.log(`   ${status._id}: ${status.count} leads`);
    });
    
    const assignmentSummary = await Lead.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: 'assignedTo',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $group: {
          _id: '$assignedTo',
          count: { $sum: 1 },
          staffName: { $first: { $concat: [{ $arrayElemAt: ['$user.firstName', 0] }, ' ', { $arrayElemAt: ['$user.lastName', 0] }] } }
        }
      }
    ]);
    
    console.log('\n👥 Lead Assignment Summary:');
    assignmentSummary.forEach(assignment => {
      console.log(`   ${assignment.staffName || 'Unassigned'}: ${assignment.count} leads`);
    });
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Leads import failed:', error);
    process.exit(1);
  }
}

importLeads();