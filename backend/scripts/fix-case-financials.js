const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseNumber(numberString) {
  if (!numberString) return 0;
  const cleaned = numberString.toString().replace(/[^0-9.-]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
}

function findBestCaseMatch(searchName, caseMap, caseArray) {
  if (!searchName) return null;
  
  // Direct match
  if (caseMap.has(searchName)) {
    return caseMap.get(searchName);
  }
  
  // Try partial matches - look for cases that contain key parts of the search name
  const searchLower = searchName.toLowerCase();
  
  for (const caseItem of caseArray) {
    const caseNameLower = caseItem.caseName.toLowerCase();
    
    // Check if significant parts match
    const searchWords = searchLower.split(/[\\s,_-]+/).filter(word => word.length > 2);
    const caseWords = caseNameLower.split(/[\\s,_-]+/).filter(word => word.length > 2);
    
    let matchCount = 0;
    for (const searchWord of searchWords) {
      for (const caseWord of caseWords) {
        if (caseWord.includes(searchWord) || searchWord.includes(caseWord)) {
          matchCount++;
          break;
        }
      }
    }
    
    // If at least 50% of words match, consider it a match
    if (matchCount >= Math.max(1, Math.floor(searchWords.length * 0.5))) {
      return caseItem._id;
    }
  }
  
  return null;
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function fixCaseFinancials() {
  try {
    await connectDB();
    
    console.log('🚀 Starting case financials fix...');
    
    // Build case mappings
    console.log('📋 Building case mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    console.log(`📊 Found ${cases.length} cases for financial calculations`);
    
    // Initialize financial tracking
    const caseFinancials = new Map();
    cases.forEach(caseItem => {
      caseFinancials.set(caseItem._id.toString(), {
        totalBilled: 0,
        totalPaid: 0,
        totalExpenses: 0,
        invoiceCount: 0,
        expenseCount: 0,
        caseName: caseItem.caseName
      });
    });
    
    // PROCESS INVOICES
    console.log('\\n💰 Processing invoices...');
    try {
      const invoices = await readCSV('/app/backup-data/invoices.csv');
      console.log(`📄 Found ${invoices.length} invoice records to process`);
      
      let invoicesProcessed = 0;
      let invoicesMatched = 0;
      
      for (const invoiceData of invoices) {
        try {
          const caseName = invoiceData['Case Name'] || '';
          const caseId = findBestCaseMatch(caseName, caseMap, cases);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const totalAmount = parseNumber(invoiceData['Total amount']) || 0;
            const paidAmount = parseNumber(invoiceData['Paid amount']) || 0;
            
            // Debug first few matches
            if (invoicesMatched < 5) {
              console.log(`💰 Invoice data: Total=${invoiceData['Total amount']}, Paid=${invoiceData['Paid amount']}`);
            }
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              financials.totalBilled += totalAmount;
              financials.totalPaid += paidAmount;
              financials.invoiceCount++;
              invoicesMatched++;
              
              if (invoicesMatched <= 5) {
                console.log(`💰 Matched invoice "${caseName}" -> "${financials.caseName}" ($${totalAmount})`);
              }
            }
          }
          invoicesProcessed++;
        } catch (error) {
          console.error(`Error processing invoice:`, error.message);
        }
      }
      
      console.log(`✅ Processed ${invoicesProcessed} invoices, matched ${invoicesMatched}`);
    } catch (error) {
      console.error('Error reading invoices CSV:', error);
    }
    
    // PROCESS EXPENSES
    console.log('\\n💸 Processing expenses...');
    try {
      const expenses = await readCSV('/app/backup-data/expenses.csv');
      console.log(`📄 Found ${expenses.length} expense records to process`);
      
      let expensesProcessed = 0;
      let expensesMatched = 0;
      
      for (const expenseData of expenses) {
        try {
          const caseName = expenseData['Case Name'] || '';
          const caseId = findBestCaseMatch(caseName, caseMap, cases);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const totalCost = parseNumber(expenseData['Total']) || 0;
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              financials.totalExpenses += totalCost;
              financials.expenseCount++;
              expensesMatched++;
              
              if (expensesMatched <= 5) {
                console.log(`💸 Matched expense "${caseName}" -> "${financials.caseName}" ($${totalCost})`);
              }
            }
          }
          expensesProcessed++;
        } catch (error) {
          console.error(`Error processing expense:`, error.message);
        }
      }
      
      console.log(`✅ Processed ${expensesProcessed} expenses, matched ${expensesMatched}`);
    } catch (error) {
      console.error('Error reading expenses CSV:', error);
    }
    
    // UPDATE CASES WITH FINANCIAL DATA
    console.log('\\n🔄 Updating case financial totals...');
    let casesUpdated = 0;
    
    for (const [caseIdStr, financials] of caseFinancials.entries()) {
      try {
        if (financials.totalBilled > 0 || financials.totalPaid > 0 || financials.totalExpenses > 0) {
          await Case.findByIdAndUpdate(caseIdStr, {
            totalBilled: financials.totalBilled,
            totalPaid: financials.totalPaid,
            caseBalance: financials.totalBilled - financials.totalPaid
          });
          casesUpdated++;
          
          if (casesUpdated <= 10) {
            console.log(`🔄 Updated case "${financials.caseName}": Billed $${financials.totalBilled}, Paid $${financials.totalPaid}`);
          }
        }
      } catch (error) {
        console.error(`Error updating case ${caseIdStr}:`, error.message);
      }
    }
    
    console.log('\\n🎉 FINANCIAL UPDATE COMPLETED!');
    console.log(`✅ Updated ${casesUpdated} cases with financial data`);
    
    // Show summary statistics
    let totalBilled = 0;
    let totalPaid = 0;
    let totalExpenses = 0;
    
    for (const financials of caseFinancials.values()) {
      totalBilled += financials.totalBilled;
      totalPaid += financials.totalPaid;
      totalExpenses += financials.totalExpenses;
    }
    
    console.log(`💰 Total Billed: $${totalBilled.toLocaleString()}`);
    console.log(`💵 Total Paid: $${totalPaid.toLocaleString()}`);
    console.log(`💸 Total Expenses: $${totalExpenses.toLocaleString()}`);
    console.log(`📊 Outstanding: $${(totalBilled - totalPaid).toLocaleString()}`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Calculation failed:', error);
    process.exit(1);
  }
}

fixCaseFinancials();