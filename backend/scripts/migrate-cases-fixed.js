const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseDate(dateString) {
  if (!dateString) return null;
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

function parseNumber(numberString) {
  if (!numberString) return 0;
  return parseFloat(numberString.toString().replace(/[^\\d.-]/g, '')) || 0;
}

function extractClientName(contactsField, clientNameField, caseName) {
  // First try the dedicated Client Name field
  if (clientNameField && clientNameField.trim() && clientNameField !== 'undefined' && clientNameField.length > 5) {
    return clientNameField.trim().replace(/\s*\([^)]*\)/g, ''); // Remove parenthetical info
  }
  
  // Try to extract from case name
  if (caseName) {
    // Pattern: "ClientName - Matter" or "ClientName_ Matter" or "ClientName: Matter"
    const caseNamePatterns = [
      /^([^_:-]+)_/,  // Before underscore
      /^([^_:-]+)-/,  // Before dash
      /^([^_:-]+):/,  // Before colon
      /([A-Z][a-z]+ [A-Z][a-z]+)/  // First proper name pattern
    ];
    
    for (const pattern of caseNamePatterns) {
      const match = caseName.match(pattern);
      if (match && match[1] && match[1].trim().length > 3) {
        const extracted = match[1].trim();
        // Don't use company names or generic terms
        if (!extracted.toLowerCase().includes('inc') && 
            !extracted.toLowerCase().includes('corp') &&
            !extracted.toLowerCase().includes('company') &&
            extracted.includes(' ')) {
          return extracted;
        }
      }
    }
  }
  
  // Fall back to extracting from contacts field
  if (!contactsField) return 'Unknown Client';
  
  // Look for "(Client)" pattern (case insensitive)
  const clientMatch = contactsField.match(/([^\n\r]+?)\s*\(Client\)/i);
  if (clientMatch) {
    return clientMatch[1].trim();
  }
  
  // Look for lines that mention client
  const lines = contactsField.split(/[\n\r]+/);
  for (const line of lines) {
    if (line && line.toLowerCase().includes('client')) {
      const cleaned = line.trim().replace(/\s*\([^)]*\)/g, '');
      if (cleaned && !cleaned.toLowerCase().includes('attorney') && !cleaned.toLowerCase().includes('manager')) {
        return cleaned;
      }
    }
  }
  
  return 'Unknown Client';
}

function getCaseType(practiceArea) {
  if (!practiceArea) return 'other';
  
  const area = practiceArea.toLowerCase();
  if (area.includes('immigration')) return 'immigration';
  if (area.includes('personal injury') || area.includes('pi')) return 'personal_injury';
  if (area.includes('criminal')) return 'criminal_defense';
  if (area.includes('family') || area.includes('divorce')) return 'family_law';
  if (area.includes('business') || area.includes('corporate')) return 'business_law';
  if (area.includes('real estate') || area.includes('property')) return 'real_estate';
  if (area.includes('employment') || area.includes('labor')) return 'employment_law';
  if (area.includes('landlord') || area.includes('tenant')) return 'other';
  
  return 'other';
}

function getCaseStatus(closedField, closedDate) {
  if (closedField === 'true' || closedDate) return 'closed';
  return 'active';
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function migrateCasesFixed() {
  try {
    await connectDB();
    
    console.log('🚀 Starting FIXED cases migration...');
    
    // Clear existing cases
    console.log('🗑️ Clearing existing cases...');
    await Case.deleteMany({});
    
    // Build user mappings
    console.log('📋 Building user mappings...');
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${users.length} users for mapping`);
    
    // Read cases CSV
    const cases = await readCSV('/app/backup-data/cases.csv');
    console.log(`📄 Found ${cases.length} case records to process`);
    
    let imported = 0;
    let errors = 0;
    let skipped = 0;
    
    for (const caseData of cases) {
      try {
        // Extract client name properly
        const caseName = caseData['Case/Matter Name'] || 'Untitled Case';
        const clientName = extractClientName(caseData['Contacts'], caseData['Client Name'], caseName);
        
        // Find lead attorney
        const leadAttorneyName = caseData['Lead Attorney'] || caseData['Lead Attorney Name'] || '';
        const leadAttorney = userMap.get(leadAttorneyName) || users[0]?._id;
        
        if (!leadAttorney) {
          console.log(`⚠️ Skipping case: No attorney found for "${leadAttorneyName}"`);
          skipped++;
          continue;
        }
        
        // Parse dates
        const openDate = parseDate(caseData['Open Date']) || new Date();
        const closedDate = parseDate(caseData['Closed Date']);
        const solDate = parseDate(caseData['SOL Date']);
        
        // Parse financial data
        const flatFee = parseNumber(caseData['Flat fee']) || 0;
        const contractValue = parseNumber(caseData['CONTRACT VALUE']) || 0;
        const outstandingBalance = parseNumber(caseData['Outstanding Balance']) || 0;
        const retainerAmount = parseNumber(caseData['Retainer']) || 0;
        const paidFee = parseNumber(caseData['Paid Fee']) || 0;
        const earnedAttorneyFee = parseNumber(caseData['Earned Attorney Fee']) || 0;
        
        // Create case with all available data
        const newCase = new Case({
          caseNumber: caseData['Number'] || `CASE-${imported + 1}`,
          caseName: caseName,
          clientName: clientName,
          clientEmail: `client${caseData['MyCase ID'] || imported}@migrated.local`,
          clientPhone: caseData['Phone No.'] || 'N/A',
          caseType: getCaseType(caseData['Practice Area']),
          practiceArea: caseData['Practice Area'] || 'General',
          description: caseData['Case Description'] || 'Migrated case',
          openDate,
          closedDate,
          solDate,
          status: getCaseStatus(caseData['Case Closed'], closedDate),
          attorney: leadAttorney,
          flatFee,
          contractValue,
          caseBalance: outstandingBalance,
          retainer: {
            amount: retainerAmount,
            received: retainerAmount,
            balance: Math.max(0, retainerAmount - paidFee)
          },
          earnedAttorneyFee,
          paidFee,
          billingType: caseData['Billing Type'] || 'hourly',
          referralSource: caseData['Where clients come from'] || '',
          conflictCheck: caseData['Conflict Check?'] === 'true',
          conflictCheckNotes: caseData['Conflict Check Notes'] || '',
          alienNumber: caseData['Alien Number'] || caseData['Alien Number (required for court cases)'] || '',
          caseStage: caseData['Case Stage'] || '',
          contacts: caseData['Contacts'] || '',
          importantDeadlines: caseData['IMPORTANT DEADLINES'] || '',
          deadlineDescription: caseData['DEADLINE DESCRIPTION'] || '',
          contractDate: parseDate(caseData['DATE OF CONTRACT SIGNED']) || parseDate(caseData['Date of K']),
          dueDateForPayment: parseDate(caseData['Due Date']),
          otherParentInfo: {
            name: caseData["Other Parent's Name"] || '',
            address: caseData["Other Parent's Address"] || '',
            email: caseData["Other Parent's Email Address"] || '',
            phone: caseData["Other Parent's Phone Number"] || '',
            birthday: parseDate(caseData["Other Parent's Birthday"]),
            employer: caseData["Other Parent's Employer"] || '',
            grossIncome: parseNumber(caseData["Other Parent's Gross Monthly Income"])
          },
          mycaseId: caseData['MyCase ID'] || `case_${imported + 1}`
        });
        
        await newCase.save();
        imported++;
        
        if (imported % 100 === 0) {
          console.log(`📊 Processed ${imported} cases...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing case ${caseData['Number']}:`, error.message);
        errors++;
      }
    }
    
    console.log('\\n🎉 FIXED CASES MIGRATION COMPLETED!');
    console.log(`✅ Successfully imported: ${imported} cases`);
    console.log(`⚠️ Skipped: ${skipped} cases`);
    console.log(`❌ Errors: ${errors} cases`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateCasesFixed();