const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Task = require('../models/Task');
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseDate(dateString) {
  if (!dateString) return null;
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

function getStatusFromString(statusString) {
  if (!statusString) return 'pending';
  const status = statusString.toLowerCase();
  
  if (status.includes('complete')) return 'completed';
  if (status.includes('progress')) return 'in_progress';
  if (status.includes('cancel')) return 'cancelled';
  
  return 'pending';
}

function getPriorityFromString(priorityString) {
  if (!priorityString) return 'normal';
  const priority = priorityString.toLowerCase();
  
  if (priority.includes('urgent')) return 'urgent';
  if (priority.includes('high')) return 'high';
  if (priority.includes('low')) return 'low';
  if (priority.includes('no priority')) return 'normal';
  
  return 'normal';
}

function parseSubtasks(subtasksString) {
  if (!subtasksString || subtasksString === '0/0') return [];
  
  // Parse format like "2/5" meaning 2 completed out of 5 total
  const match = subtasksString.match(/(\d+)\/(\d+)/);
  if (match) {
    const completed = parseInt(match[1]);
    const total = parseInt(match[2]);
    
    const subtasks = [];
    for (let i = 1; i <= total; i++) {
      subtasks.push({
        name: `Subtask ${i}`,
        completed: i <= completed,
        completedAt: i <= completed ? new Date() : null
      });
    }
    return subtasks;
  }
  
  return [];
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function migrateTasks() {
  try {
    await connectDB();
    
    console.log('🚀 Starting tasks migration...');
    
    // Build case and user mappings
    console.log('📋 Building case and user mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${cases.length} cases and ${users.length} users for mapping`);
    
    // Read tasks CSV
    const tasks = await readCSV('/app/backup-data/tasks.csv');
    console.log(`📄 Found ${tasks.length} task records to process`);
    
    let imported = 0;
    let errors = 0;
    let skipped = 0;
    
    for (const taskData of tasks) {
      try {
        // Find matching case
        const caseName = taskData['Case Name'] || '';
        const caseId = caseMap.get(caseName);
        
        if (!caseId) {
          console.log(`⚠️ Skipping task: No matching case found for "${caseName}"`);
          skipped++;
          continue;
        }
        
        // Find assigned users
        const assignedByName = taskData['Assigned By'] || '';
        const assignedToNames = taskData['Assigned To'] || '';
        const completedByName = taskData['Completed By'] || '';
        
        const assignedBy = userMap.get(assignedByName);
        const completedBy = userMap.get(completedByName);
        
        // Parse assigned to (could be multiple users)
        let assignedTo = [];
        if (assignedToNames) {
          const names = assignedToNames.split(',').map(n => n.trim());
          assignedTo = names.map(name => userMap.get(name)).filter(id => id);
        }
        
        if (!assignedBy) {
          console.log(`⚠️ Skipping task: No matching assigned by user found for "${assignedByName}"`);
          skipped++;
          continue;
        }
        
        // Check if task already exists
        const existingTask = await Task.findOne({
          mycase_id: taskData['Mycase Id']
        });
        
        if (existingTask) {
          skipped++;
          continue;
        }
        
        // Parse status and dates
        const status = getStatusFromString(taskData['Status']);
        const priority = getPriorityFromString(taskData['Priority']);
        const dueDate = parseDate(taskData['Due Date']);
        const completedAt = parseDate(taskData['Completed At']);
        
        // Create task
        const newTask = new Task({
          name: taskData['Task Name'] || 'Untitled Task',
          description: taskData['Description'] || '',
          status,
          priority,
          dueDate,
          completedAt,
          totalTaskEstimate: parseFloat(taskData['Total Task Estimate']) || 0,
          caseId,
          assignedBy,
          assignedTo: assignedTo.length > 0 ? assignedTo[0] : assignedBy, // Take first assigned user
          completedBy,
          subtasks: parseSubtasks(taskData['Subtasks']),
          archived: taskData['Archived'] === 'true',
          mycase_id: taskData['Mycase Id']
        });
        
        await newTask.save();
        imported++;
        
        if (imported % 100 === 0) {
          console.log(`📊 Processed ${imported} tasks...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing task:`, error.message);
        errors++;
      }
    }
    
    console.log('\n🎉 TASKS MIGRATION COMPLETED!');
    console.log(`✅ Successfully imported: ${imported} tasks`);
    console.log(`⚠️ Skipped: ${skipped} tasks`);
    console.log(`❌ Errors: ${errors} tasks`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateTasks();