const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseNumber(numberString) {
  if (!numberString) return 0;
  return parseFloat(numberString.toString().replace(/[^\\d.-]/g, '')) || 0;
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function calculateCaseFinancials() {
  try {
    await connectDB();
    
    console.log('🚀 Starting case financials calculation...');
    
    // Build case mappings
    console.log('📋 Building case mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    console.log(`📊 Found ${cases.length} cases for financial calculations`);
    
    // Initialize financial tracking
    const caseFinancials = new Map();
    cases.forEach(caseItem => {
      caseFinancials.set(caseItem._id.toString(), {
        totalBilled: 0,
        totalPaid: 0,
        totalExpenses: 0,
        invoiceCount: 0,
        expenseCount: 0
      });
    });
    
    // PROCESS INVOICES
    console.log('\\n💰 Processing invoices...');
    try {
      const invoices = await readCSV('/app/backup-data/invoices.csv');
      console.log(`📄 Found ${invoices.length} invoice records to process`);
      
      let invoicesProcessed = 0;
      
      for (const invoiceData of invoices) {
        try {
          const caseName = invoiceData['Case Name'] || '';
          const caseId = caseMap.get(caseName);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const totalAmount = parseNumber(invoiceData['Total amount']) || 0;
            const paidAmount = parseNumber(invoiceData['Paid amount']) || 0;
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              financials.totalBilled += totalAmount;
              financials.totalPaid += paidAmount;
              financials.invoiceCount++;
              invoicesProcessed++;
            }
          }
        } catch (error) {
          console.error(`Error processing invoice:`, error.message);
        }
      }
      
      console.log(`✅ Processed ${invoicesProcessed} invoices`);
    } catch (error) {
      console.error('Error reading invoices CSV:', error);
    }
    
    // PROCESS EXPENSES
    console.log('\\n💸 Processing expenses...');
    try {
      const expenses = await readCSV('/app/backup-data/expenses.csv');
      console.log(`📄 Found ${expenses.length} expense records to process`);
      
      let expensesProcessed = 0;
      
      for (const expenseData of expenses) {
        try {
          const caseName = expenseData['Case Name'] || '';
          const caseId = caseMap.get(caseName);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const totalCost = parseNumber(expenseData['Total']) || 0;
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              financials.totalExpenses += totalCost;
              financials.expenseCount++;
              expensesProcessed++;
            }
          }
        } catch (error) {
          console.error(`Error processing expense:`, error.message);
        }
      }
      
      console.log(`✅ Processed ${expensesProcessed} expenses`);
    } catch (error) {
      console.error('Error reading expenses CSV:', error);
    }
    
    // UPDATE CASES WITH FINANCIAL DATA
    console.log('\\n🔄 Updating case financial totals...');
    let casesUpdated = 0;
    
    for (const [caseIdStr, financials] of caseFinancials.entries()) {
      try {
        if (financials.totalBilled > 0 || financials.totalPaid > 0 || financials.totalExpenses > 0) {
          await Case.findByIdAndUpdate(caseIdStr, {
            totalBilled: financials.totalBilled,
            totalPaid: financials.totalPaid,
            caseBalance: financials.totalBilled - financials.totalPaid,
            // You might want to track expenses separately or include in balance
            totalExpenses: financials.totalExpenses
          });
          casesUpdated++;
        }
      } catch (error) {
        console.error(`Error updating case ${caseIdStr}:`, error.message);
      }
    }
    
    console.log('\\n🎉 FINANCIAL CALCULATION COMPLETED!');
    console.log(`✅ Updated ${casesUpdated} cases with financial data`);
    
    // Show summary statistics
    let totalBilled = 0;
    let totalPaid = 0;
    let totalExpenses = 0;
    
    for (const financials of caseFinancials.values()) {
      totalBilled += financials.totalBilled;
      totalPaid += financials.totalPaid;
      totalExpenses += financials.totalExpenses;
    }
    
    console.log(`💰 Total Billed: $${totalBilled.toLocaleString()}`);
    console.log(`💵 Total Paid: $${totalPaid.toLocaleString()}`);
    console.log(`💸 Total Expenses: $${totalExpenses.toLocaleString()}`);
    console.log(`📊 Outstanding: $${(totalBilled - totalPaid).toLocaleString()}`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Calculation failed:', error);
    process.exit(1);
  }
}

calculateCaseFinancials();