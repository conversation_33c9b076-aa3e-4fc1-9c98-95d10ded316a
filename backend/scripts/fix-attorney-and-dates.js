const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

function parseDate(dateString) {
  if (!dateString || dateString === '') return null;
  
  try {
    // Handle MM/DD/YYYY format
    const cleanDate = dateString.trim();
    if (cleanDate.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
      const [month, day, year] = cleanDate.split('/');
      return new Date(year, month - 1, day);
    }
    
    // Try standard date parsing
    const parsed = new Date(cleanDate);
    if (!isNaN(parsed.getTime())) {
      return parsed;
    }
  } catch (error) {
    console.error(`Failed to parse date: ${dateString}`);
  }
  
  return null;
}

function extractAttorneyFromContacts(contactsField) {
  if (!contactsField) return null;
  
  const lines = contactsField.split('\n').map(line => line.trim()).filter(line => line);
  
  for (const line of lines) {
    if (line.toLowerCase().includes('(attorney)')) {
      return line.replace(/\s*\(attorney\)\s*/i, '').trim();
    }
  }
  
  return null;
}

async function fixAttorneyAndDates() {
  try {
    await connectDB();
    
    console.log('🚀 Starting attorney assignments and date corrections...');
    
    // Get user mappings for attorney assignments
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
      console.log(`👤 Available attorney: ${fullName}`);
    });
    
    console.log(`👥 Found ${users.length} users for attorney mapping`);
    
    // Read cases CSV
    const casesData = await readCSV('/app/backup-data/cases.csv');
    console.log(`📄 Found ${casesData.length} cases to process`);
    
    let casesUpdated = 0;
    let attorneysMatched = 0;
    let datesUpdated = 0;
    
    for (const csvCase of casesData) {
      try {
        const caseName = csvCase['Case/Matter Name'];
        if (!caseName) continue;
        
        // Find existing case in database
        const existingCase = await Case.findOne({ caseName });
        if (!existingCase) continue;
        
        const updateData = {};
        
        // Extract and assign attorney
        const leadAttorney = csvCase['Lead Attorney'] || csvCase['Lead Attorney Name'] || '';
        const originatingAttorney = csvCase['Originating Attorney'] || csvCase['Originating Attorney Name'] || '';
        const contactsAttorney = extractAttorneyFromContacts(csvCase['Contacts']);
        
        // Try to find the best attorney match
        let attorneyToAssign = leadAttorney || originatingAttorney || contactsAttorney;
        
        if (attorneyToAssign && userMap.has(attorneyToAssign)) {
          updateData.attorney = userMap.get(attorneyToAssign);
          attorneysMatched++;
        } else if (attorneyToAssign) {
          // Try partial matching for common name variations
          for (const [userName, userId] of userMap.entries()) {
            if (userName.toLowerCase().includes(attorneyToAssign.toLowerCase()) || 
                attorneyToAssign.toLowerCase().includes(userName.toLowerCase())) {
              updateData.attorney = userId;
              attorneysMatched++;
              break;
            }
          }
        }
        
        // Parse and set open date as created date
        const openDate = parseDate(csvCase['Open Date']);
        if (openDate) {
          updateData.openDate = openDate;
          updateData.createdAt = openDate;
          updateData.updatedAt = openDate;
          datesUpdated++;
        }
        
        // Update the case if we have changes
        if (Object.keys(updateData).length > 0) {
          await Case.findByIdAndUpdate(existingCase._id, updateData);
          casesUpdated++;
          
          // Show progress for first 10 updates
          if (casesUpdated <= 10) {
            console.log(`🔄 Updated "${caseName}"`);
            console.log(`   Attorney: ${attorneyToAssign || 'Not found'} -> ${updateData.attorney ? 'Assigned' : 'Not assigned'}`);
            console.log(`   Open Date: ${csvCase['Open Date']} -> ${openDate ? openDate.toLocaleDateString() : 'Not parsed'}`);
            console.log('---');
          }
        }
        
      } catch (error) {
        console.error(`Error processing case: ${error.message}`);
      }
    }
    
    console.log('\n🎉 ATTORNEY AND DATE UPDATE COMPLETED!');
    console.log(`✅ Updated ${casesUpdated} cases`);
    console.log(`👨‍💼 Matched ${attorneysMatched} attorney assignments`);
    console.log(`📅 Updated ${datesUpdated} case open dates`);
    
    // Show sample of updated data
    console.log('\n📊 Sample of updated cases:');
    const samples = await Case.find({}).limit(5)
      .select('caseName clientName openDate createdAt attorney')
      .populate('attorney', 'firstName lastName');
    
    samples.forEach((c, i) => {
      console.log(`${i+1}. Case: ${c.caseName}`);
      console.log(`   Client: ${c.clientName}`);
      console.log(`   Attorney: ${c.attorney ? `${c.attorney.firstName} ${c.attorney.lastName}` : 'Not assigned'}`);
      console.log(`   Open Date: ${c.openDate ? c.openDate.toLocaleDateString() : 'Not set'}`);
      console.log(`   Created At: ${c.createdAt ? c.createdAt.toLocaleDateString() : 'Not set'}`);
      console.log('---');
    });
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Attorney and date fix failed:', error);
    process.exit(1);
  }
}

fixAttorneyAndDates();