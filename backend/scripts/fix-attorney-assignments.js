const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

function findBestAttorneyMatch(attorneyName, userMap) {
  if (!attorneyName || attorneyName.trim() === '') return null;
  
  const searchName = attorneyName.trim();
  
  // Direct match
  if (userMap.has(searchName)) {
    return userMap.get(searchName);
  }
  
  // Handle "Yuchen <PERSON>ie <PERSON>" -> "Yuchen Zhang" type mappings
  const searchLower = searchName.toLowerCase();
  
  // Special case for Yuchen
  if (searchLower.includes('yuchen') && searchLower.includes('zhang')) {
    for (const [userName, userId] of userMap.entries()) {
      if (userName.toLowerCase().includes('yuchen') && userName.toLowerCase().includes('zhang')) {
        return userId;
      }
    }
  }
  
  // Try partial matching - split names and find best match
  const searchWords = searchName.toLowerCase().split(/\s+/);
  let bestMatch = null;
  let bestScore = 0;
  
  for (const [userName, userId] of userMap.entries()) {
    const userWords = userName.toLowerCase().split(/\s+/);
    let score = 0;
    
    // Count matching words
    for (const searchWord of searchWords) {
      for (const userWord of userWords) {
        if (searchWord === userWord || 
            searchWord.includes(userWord) || 
            userWord.includes(searchWord)) {
          score++;
          break;
        }
      }
    }
    
    // Calculate match percentage
    const matchPercentage = score / Math.max(searchWords.length, userWords.length);
    
    if (matchPercentage > 0.5 && score > bestScore) {
      bestScore = score;
      bestMatch = userId;
    }
  }
  
  return bestMatch;
}

async function fixAttorneyAssignments() {
  try {
    await connectDB();
    
    console.log('🚀 Starting attorney assignment corrections...');
    
    // Get user mappings for attorney assignments
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log('👥 Available attorneys in database:');
    for (const [name, id] of userMap.entries()) {
      console.log(`   ${name}`);
    }
    
    // Read cases CSV
    const casesData = await readCSV('/app/backup-data/cases.csv');
    console.log(`📄 Found ${casesData.length} cases to process for attorney assignment`);
    
    let casesUpdated = 0;
    let attorneysMatched = 0;
    const attorneyStats = new Map();
    
    for (const csvCase of casesData) {
      try {
        const caseName = csvCase['Case/Matter Name'];
        if (!caseName) continue;
        
        // Find existing case in database
        const existingCase = await Case.findOne({ caseName });
        if (!existingCase) continue;
        
        // Extract attorney from multiple possible fields
        const leadAttorney = csvCase['Lead Attorney'] || '';
        const originatingAttorney = csvCase['Originating Attorney'] || '';
        const leadAttorneyName = csvCase['Lead Attorney Name'] || '';
        const originatingAttorneyName = csvCase['Originating Attorney Name'] || '';
        
        // Priority order: Lead Attorney Name, Lead Attorney, Originating Attorney Name, Originating Attorney
        const attorneyToMatch = leadAttorneyName || leadAttorney || originatingAttorneyName || originatingAttorney;
        
        if (attorneyToMatch) {
          const attorneyId = findBestAttorneyMatch(attorneyToMatch, userMap);
          
          if (attorneyId) {
            await Case.findByIdAndUpdate(existingCase._id, {
              attorney: attorneyId
            });
            
            attorneysMatched++;
            
            // Track attorney assignment stats
            if (!attorneyStats.has(attorneyToMatch)) {
              attorneyStats.set(attorneyToMatch, 0);
            }
            attorneyStats.set(attorneyToMatch, attorneyStats.get(attorneyToMatch) + 1);
            
            if (attorneysMatched <= 10) {
              console.log(`👨‍💼 Assigned "${attorneyToMatch}" to case "${caseName}"`);
            }
          } else if (attorneysMatched <= 10) {
            console.log(`❌ Could not match attorney "${attorneyToMatch}" for case "${caseName}"`);
          }
        }
        
        casesUpdated++;
        
      } catch (error) {
        console.error(`Error processing case: ${error.message}`);
      }
    }
    
    console.log('\n🎉 ATTORNEY ASSIGNMENT UPDATE COMPLETED!');
    console.log(`✅ Processed ${casesUpdated} cases`);
    console.log(`👨‍💼 Successfully matched ${attorneysMatched} attorney assignments`);
    
    console.log('\n📊 Attorney assignment statistics:');
    for (const [attorneyName, count] of attorneyStats.entries()) {
      console.log(`   ${attorneyName}: ${count} cases`);
    }
    
    // Show sample of updated data
    console.log('\n📋 Sample of updated cases:');
    const samples = await Case.find({}).limit(5)
      .select('caseName clientName attorney')
      .populate('attorney', 'firstName lastName');
    
    samples.forEach((c, i) => {
      console.log(`${i+1}. Case: ${c.caseName}`);
      console.log(`   Client: ${c.clientName}`);
      console.log(`   Attorney: ${c.attorney ? `${c.attorney.firstName} ${c.attorney.lastName}` : 'Not assigned'}`);
      console.log('---');
    });
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Attorney assignment fix failed:', error);
    process.exit(1);
  }
}

fixAttorneyAssignments();