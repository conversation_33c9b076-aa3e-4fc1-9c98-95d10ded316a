const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Case = require('../models/Case');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseNumber(numberString) {
  if (!numberString) return 0;
  const cleaned = numberString.toString().replace(/[^0-9.-]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
}

function findBestCaseMatch(searchName, caseMap, caseArray) {
  if (!searchName) return null;
  
  // Direct match
  if (caseMap.has(searchName)) {
    return caseMap.get(searchName);
  }
  
  // Try partial matches
  const searchLower = searchName.toLowerCase();
  
  for (const caseItem of caseArray) {
    const caseNameLower = caseItem.caseName.toLowerCase();
    
    // Check if significant parts match
    const searchWords = searchLower.split(/[\\s,_-]+/).filter(word => word.length > 2);
    const caseWords = caseNameLower.split(/[\\s,_-]+/).filter(word => word.length > 2);
    
    let matchCount = 0;
    for (const searchWord of searchWords) {
      for (const caseWord of caseWords) {
        if (caseWord.includes(searchWord) || searchWord.includes(caseWord)) {
          matchCount++;
          break;
        }
      }
    }
    
    // If at least 50% of words match, consider it a match
    if (matchCount >= Math.max(1, Math.floor(searchWords.length * 0.5))) {
      return caseItem._id;
    }
  }
  
  return null;
}

function isUSCISFee(activity, description) {
  if (!activity && !description) return false;
  
  const text = `${activity || ''} ${description || ''}`.toLowerCase();
  
  return text.includes('filing fee') || 
         text.includes('uscis') ||
         text.includes('i-130') ||
         text.includes('i-485') ||
         text.includes('i-140') ||
         text.includes('i-539') ||
         text.includes('i-765') ||
         text.includes('i-131') ||
         text.includes('n-400') ||
         text.includes('n400') ||
         text.includes('i589') ||
         text.includes('i-589') ||
         text.includes('government fee') ||
         text.includes('petition fee');
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function categorizeCaseFinancials() {
  try {
    await connectDB();
    
    console.log('🚀 Starting categorized case financials calculation...');
    
    // Build case mappings
    console.log('📋 Building case mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1, retainer: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    console.log(`📊 Found ${cases.length} cases for financial calculations`);
    
    // Initialize financial tracking with categories
    const caseFinancials = new Map();
    cases.forEach(caseItem => {
      caseFinancials.set(caseItem._id.toString(), {
        totalBilled: 0,
        totalPaid: 0,
        remainingBalance: 0,
        expenses: 0,
        uscisFilingFees: 0,
        retainerAmount: caseItem.retainer?.amount || 0,
        retainerBalance: caseItem.retainer?.balance || 0,
        caseName: caseItem.caseName
      });
    });
    
    // PROCESS FLAT FEES (Attorney Fees)
    console.log('\\n💼 Processing flat fees (attorney fees)...');
    try {
      const flatFees = await readCSV('/app/backup-data/flat_fees.csv');
      console.log(`📄 Found ${flatFees.length} flat fee records to process`);
      
      let flatFeesMatched = 0;
      
      for (const feeData of flatFees) {
        try {
          const caseName = feeData['Case Name'] || '';
          const caseId = findBestCaseMatch(caseName, caseMap, cases);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const amount = parseNumber(feeData['Amount']) || 0;
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              financials.totalBilled += amount;
              flatFeesMatched++;
            }
          }
        } catch (error) {
          console.error(`Error processing flat fee:`, error.message);
        }
      }
      
      console.log(`✅ Processed ${flatFeesMatched} flat fees (attorney fees)`);
    } catch (error) {
      console.error('Error reading flat fees CSV:', error);
    }
    
    // PROCESS INVOICES (Payments)
    console.log('\\n💰 Processing invoices (payments)...');
    try {
      const invoices = await readCSV('/app/backup-data/invoices.csv');
      console.log(`📄 Found ${invoices.length} invoice records to process`);
      
      let invoicesMatched = 0;
      
      for (const invoiceData of invoices) {
        try {
          const caseName = invoiceData['Case Name'] || '';
          const caseId = findBestCaseMatch(caseName, caseMap, cases);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const paidAmount = parseNumber(invoiceData['Paid amount']) || 0;
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              financials.totalPaid += paidAmount;
              invoicesMatched++;
            }
          }
        } catch (error) {
          console.error(`Error processing invoice:`, error.message);
        }
      }
      
      console.log(`✅ Processed ${invoicesMatched} invoices (payments)`);
    } catch (error) {
      console.error('Error reading invoices CSV:', error);
    }
    
    // PROCESS EXPENSES (Separate USCIS fees from other expenses)
    console.log('\\n💸 Processing expenses (USCIS fees vs general expenses)...');
    try {
      const expenses = await readCSV('/app/backup-data/expenses.csv');
      console.log(`📄 Found ${expenses.length} expense records to process`);
      
      let expensesMatched = 0;
      let uscisFeesMatched = 0;
      
      for (const expenseData of expenses) {
        try {
          const caseName = expenseData['Case Name'] || '';
          const caseId = findBestCaseMatch(caseName, caseMap, cases);
          
          if (caseId) {
            const caseIdStr = caseId.toString();
            const totalCost = parseNumber(expenseData['Total']) || 0;
            const activity = expenseData['Activity'] || '';
            const description = expenseData['Description'] || '';
            
            if (caseFinancials.has(caseIdStr)) {
              const financials = caseFinancials.get(caseIdStr);
              
              if (isUSCISFee(activity, description)) {
                financials.uscisFilingFees += totalCost;
                uscisFeesMatched++;
              } else {
                financials.expenses += totalCost;
                expensesMatched++;
              }
            }
          }
        } catch (error) {
          console.error(`Error processing expense:`, error.message);
        }
      }
      
      console.log(`✅ Processed expenses: ${expensesMatched} general, ${uscisFeesMatched} USCIS filing fees`);
    } catch (error) {
      console.error('Error reading expenses CSV:', error);
    }
    
    // CALCULATE BALANCES AND UPDATE CASES
    console.log('\\n🔄 Calculating balances and updating cases...');
    let casesUpdated = 0;
    
    for (const [caseIdStr, financials] of caseFinancials.entries()) {
      try {
        // Calculate remaining balance
        financials.remainingBalance = financials.totalBilled - financials.totalPaid;
        
        if (financials.totalBilled > 0 || financials.totalPaid > 0 || 
            financials.expenses > 0 || financials.uscisFilingFees > 0) {
          
          await Case.findByIdAndUpdate(caseIdStr, {
            totalBilled: financials.totalBilled,
            totalPaid: financials.totalPaid,
            caseBalance: financials.remainingBalance,
            financials: {
              totalBilled: financials.totalBilled,
              totalPaid: financials.totalPaid,
              remainingBalance: financials.remainingBalance,
              expenses: financials.expenses,
              uscisFilingFees: financials.uscisFilingFees,
              retainerAmount: financials.retainerAmount,
              retainerBalance: financials.retainerBalance
            }
          });
          casesUpdated++;
          
          if (casesUpdated <= 10) {
            console.log(`🔄 Updated "${financials.caseName}": Billed $${financials.totalBilled}, Paid $${financials.totalPaid}, USCIS $${financials.uscisFilingFees}, Expenses $${financials.expenses}`);
          }
        }
      } catch (error) {
        console.error(`Error updating case ${caseIdStr}:`, error.message);
      }
    }
    
    console.log('\\n🎉 CATEGORIZED FINANCIAL UPDATE COMPLETED!');
    console.log(`✅ Updated ${casesUpdated} cases with categorized financial data`);
    
    // Show summary statistics
    let totalBilled = 0;
    let totalPaid = 0;
    let totalExpenses = 0;
    let totalUSCISFees = 0;
    let totalRetainer = 0;
    
    for (const financials of caseFinancials.values()) {
      totalBilled += financials.totalBilled;
      totalPaid += financials.totalPaid;
      totalExpenses += financials.expenses;
      totalUSCISFees += financials.uscisFilingFees;
      totalRetainer += financials.retainerAmount;
    }
    
    console.log('\\n📊 FINANCIAL SUMMARY:');
    console.log(`💼 Total Attorney Fees Billed: $${totalBilled.toLocaleString()}`);
    console.log(`💵 Total Paid: $${totalPaid.toLocaleString()}`);
    console.log(`💸 Total General Expenses: $${totalExpenses.toLocaleString()}`);
    console.log(`🏛️  Total USCIS Filing Fees: $${totalUSCISFees.toLocaleString()}`);
    console.log(`🏦 Total Retainer Amount: $${totalRetainer.toLocaleString()}`);
    console.log(`📊 Outstanding Balance: $${(totalBilled - totalPaid).toLocaleString()}`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Calculation failed:', error);
    process.exit(1);
  }
}

categorizeCaseFinancials();