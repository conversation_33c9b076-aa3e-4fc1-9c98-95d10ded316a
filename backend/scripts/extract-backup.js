#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const extractBackup = async () => {
  console.log('=== Backup Extraction Tool ===\n');
  
  // Get zip file path from command line arguments
  const zipPath = process.argv[2];
  
  if (!zipPath) {
    console.error('Error: Please provide the path to the backup zip file');
    console.log('Usage: node extract-backup.js <path-to-backup-zip>');
    process.exit(1);
  }
  
  // Verify zip file exists
  if (!fs.existsSync(zipPath)) {
    console.error(`Error: Backup zip file not found: ${zipPath}`);
    process.exit(1);
  }
  
  // Create extraction directory
  const extractDir = path.join(__dirname, '../backup-data');
  
  // Remove existing extraction directory if it exists
  if (fs.existsSync(extractDir)) {
    console.log('Removing existing backup-data directory...');
    fs.rmSync(extractDir, { recursive: true, force: true });
  }
  
  // Create new extraction directory
  fs.mkdirSync(extractDir, { recursive: true });
  
  console.log(`Extracting ${zipPath} to ${extractDir}...`);
  
  try {
    // Extract the zip file
    execSync(`unzip -q "${zipPath}" -d "${extractDir}"`, { stdio: 'inherit' });
    
    // List extracted files
    const files = fs.readdirSync(extractDir);
    console.log(`\nExtracted ${files.length} files:`);
    files.forEach(file => {
      const filePath = path.join(extractDir, file);
      const stats = fs.statSync(filePath);
      console.log(`  - ${file} (${stats.size} bytes)`);
    });
    
    console.log(`\nBackup extracted successfully to: ${extractDir}`);
    console.log('You can now run the migration with:');
    console.log(`node migrate-backup.js "${extractDir}"`);
    
  } catch (error) {
    console.error('Error extracting backup:', error.message);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  extractBackup();
}

module.exports = { extractBackup };