const mongoose = require('mongoose');
const csv = require('csv-parser');
const fs = require('fs');

// Import models
const Note = require('../models/Note');
const Case = require('../models/Case');
const User = require('../models/User');

// Database connection
async function connectDB() {
  try {
    await mongoose.connect('mongodb://mongo:27017/mycase');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error;
  }
}

// Helper functions
function parseDate(dateString) {
  if (!dateString) return null;
  const parsed = new Date(dateString);
  return isNaN(parsed) ? null : parsed;
}

function cleanHtmlContent(htmlString) {
  if (!htmlString) return '';
  
  // Basic HTML tag removal and entity decoding
  return htmlString
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .trim();
}

// Read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function migrateNotes() {
  try {
    await connectDB();
    
    console.log('🚀 Starting notes migration...');
    
    // Build case and user mappings
    console.log('📋 Building case and user mappings...');
    const cases = await Case.find({}, { _id: 1, caseName: 1, caseNumber: 1 });
    const users = await User.find({}, { _id: 1, firstName: 1, lastName: 1 });
    
    const caseMap = new Map();
    cases.forEach(caseItem => {
      caseMap.set(caseItem.caseName, caseItem._id);
    });
    
    const userMap = new Map();
    users.forEach(user => {
      const fullName = `${user.firstName} ${user.lastName}`.trim();
      userMap.set(fullName, user._id);
    });
    
    console.log(`📊 Found ${cases.length} cases and ${users.length} users for mapping`);
    
    // Read notes CSV
    const notes = await readCSV('/app/backup-data/notes.csv');
    console.log(`📄 Found ${notes.length} note records to process`);
    
    let imported = 0;
    let errors = 0;
    let skipped = 0;
    
    for (const noteData of notes) {
      try {
        // Find matching case
        const caseName = noteData['Case Name'] || '';
        const caseId = caseMap.get(caseName);
        
        if (!caseId) {
          console.log(`⚠️ Skipping note: No matching case found for "${caseName}"`);
          skipped++;
          continue;
        }
        
        // Find creating user
        const createdByName = noteData['Created By'] || '';
        const createdBy = userMap.get(createdByName);
        
        if (!createdBy) {
          console.log(`⚠️ Skipping note: No matching user found for "${createdByName}"`);
          skipped++;
          continue;
        }
        
        // Parse dates
        const noteDate = parseDate(noteData['Date']) || parseDate(noteData['Created at']) || new Date();
        
        // Clean content
        const content = cleanHtmlContent(noteData['Note']) || noteData['Subject'] || 'No content';
        const subject = noteData['Subject'] || 'Note';
        
        // Create note
        const newNote = new Note({
          subject,
          content,
          caseId,
          createdBy,
          noteDate,
          mycase_id: `note_${imported + 1}`
        });
        
        await newNote.save();
        imported++;
        
        if (imported % 500 === 0) {
          console.log(`📊 Processed ${imported} notes...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing note:`, error.message);
        errors++;
      }
    }
    
    console.log('\n🎉 NOTES MIGRATION COMPLETED!');
    console.log(`✅ Successfully imported: ${imported} notes`);
    console.log(`⚠️ Skipped: ${skipped} notes`);
    console.log(`❌ Errors: ${errors} notes`);
    
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

migrateNotes();