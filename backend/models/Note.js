const mongoose = require('mongoose');

const noteSchema = new mongoose.Schema({
  subject: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  noteDate: {
    type: Date,
    default: Date.now
  },
  tags: [String],
  isPrivate: {
    type: Boolean,
    default: false
  },
  mycase_id: String
}, {
  timestamps: true
});

module.exports = mongoose.model('Note', noteSchema);