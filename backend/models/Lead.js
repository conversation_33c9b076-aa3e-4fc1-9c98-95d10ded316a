const mongoose = require('mongoose');

const leadSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true
  },
  firstName: {
    type: String,
    trim: true
  },
  middleName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  
  // Contact Information
  contact: {
    email: {
      type: String,
      lowercase: true,
      trim: true
    },
    workPhone: String,
    homePhone: String,
    cellPhone: String
  },
  
  // Address
  address: {
    street: String,
    street2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  
  // Lead Details
  practiceArea: {
    type: String,
    trim: true
  },
  caseType: {
    type: String,
    enum: ['personal_injury', 'criminal_defense', 'family_law', 'family_law_tx', 'family_law_il', 'civil_litigation', 'business_law', 'estate_planning', 'immigration', 'employment_law', 'real_estate', 'other'],
    default: 'other'
  },
  status: {
    type: String,
    enum: ['new', 'contacted', 'qualified', 'converted', 'closed'],
    default: 'new'
  },
  priority: {
    type: Number,
    default: 0
  },
  source: {
    type: String,
    enum: ['website', 'referral', 'advertising', 'social_media', 'google', 'other'],
    default: 'other'
  },
  
  // Lead Management
  details: {
    type: String,
    trim: true
  },
  value: {
    type: Number,
    default: 0,
    min: 0
  },
  referredBy: String,
  birthday: Date,
  licenseNumber: String,
  licenseState: String,
  
  // Dates
  addedDate: {
    type: Date,
    default: Date.now
  },
  convertedDate: Date,
  followUpDate: Date,
  
  // Potential Case Information
  potentialCaseName: String,
  potentialCaseDescription: String,
  conflictCheck: {
    type: Boolean,
    default: false
  },
  conflictCheckNotes: String,
  
  // Assignment
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Notes
  notes: String,
  
  // System
  mycase_id: String
}, {
  timestamps: true
});

module.exports = mongoose.model('Lead', leadSchema);