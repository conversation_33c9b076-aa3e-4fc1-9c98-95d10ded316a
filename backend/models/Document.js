const mongoose = require('mongoose');

const documentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['contract', 'correspondence', 'filing', 'evidence', 'other'],
    default: 'other'
  },
  fileSize: {
    type: Number,
    default: 0
  },
  fileName: {
    type: String,
    trim: true
  },
  filePath: {
    type: String,
    trim: true
  },
  mimeType: {
    type: String,
    trim: true
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadDate: {
    type: Date,
    default: Date.now
  },
  tags: [String],
  isPrivate: {
    type: Boolean,
    default: false
  },
  version: {
    type: Number,
    default: 1
  },
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  },
  mycase_id: String
}, {
  timestamps: true
});

documentSchema.index({ caseId: 1, uploadDate: -1 });
documentSchema.index({ name: 'text', description: 'text' });

module.exports = mongoose.model('Document', documentSchema);