const mongoose = require('mongoose');

const timeEntrySchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true
  },
  activity: {
    type: String,
    required: true,
    trim: true
  },
  timeSpent: {
    type: Number, // in minutes
    required: true,
    min: 0
  },
  hourlyRate: {
    type: Number,
    required: true,
    min: 0
  },
  flatRate: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  invoiceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice'
  },
  nonbillable: {
    type: Boolean,
    default: false
  },
  billed: {
    type: Boolean,
    default: false
  },
  mycase_id: String // For migration tracking
}, {
  timestamps: true
});

// Calculate total before saving
timeEntrySchema.pre('save', function(next) {
  if (this.flatRate > 0) {
    this.total = this.flatRate;
  } else {
    this.total = (this.timeSpent / 60) * this.hourlyRate;
  }
  next();
});

module.exports = mongoose.model('TimeEntry', timeEntrySchema);