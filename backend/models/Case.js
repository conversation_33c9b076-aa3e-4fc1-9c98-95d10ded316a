const mongoose = require('mongoose');

const caseSchema = new mongoose.Schema({
  caseNumber: {
    type: String,
    required: true,
    unique: true
  },
  caseName: {
    type: String,
    required: true,
    trim: true
  },
  clientName: {
    type: String,
    required: true,
    trim: true
  },
  clientEmail: {
    type: String,
    required: false,
    lowercase: true,
    trim: true
  },
  clientPhone: {
    type: String,
    required: false,
    trim: true
  },
  clientAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String
  },
  caseType: {
    type: String,
    required: true,
    enum: ['personal_injury', 'criminal_defense', 'family_law', 'family_law_tx', 'family_law_il', 'civil_litigation', 'business_law', 'estate_planning', 'immigration', 'employment_law', 'real_estate', 'other']
  },
  practiceArea: {
    type: String,
    trim: true
  },
  openDate: {
    type: Date,
    default: Date.now
  },
  closedDate: Date,
  solDate: Date, // Statute of Limitations Date
  referralSource: {
    type: String,
    trim: true
  },
  referredBy: {
    type: String,
    trim: true
  },
  incident: {
    date: Date,
    location: String,
    description: String
  },
  settlement: {
    amount: {
      type: Number,
      default: 0
    },
    date: Date,
    details: String
  },
  insurance: {
    carrier: String,
    policyNumber: String,
    adjuster: {
      name: String,
      phone: String,
      email: String
    },
    claimNumber: String
  },
  opposing: {
    party: String,
    attorney: String,
    firm: String,
    phone: String,
    email: String
  },
  court: {
    name: String,
    judge: String,
    courtroom: String,
    address: String
  },
  retainer: {
    amount: {
      type: Number,
      default: 0
    },
    received: {
      type: Number,
      default: 0
    },
    balance: {
      type: Number,
      default: 0
    }
  },
  contingencyRate: {
    type: Number,
    min: 0,
    max: 100
  },
  hourlyRate: {
    type: Number,
    default: 0
  },
  flatFee: {
    type: Number,
    default: 0
  },
  currentStage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CaseStage'
  },
  tags: [{
    type: String,
    trim: true
  }],
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  archived: {
    type: Boolean,
    default: false
  },
  clientType: {
    type: String,
    enum: ['individual', 'business', 'government', 'other'],
    default: 'individual'
  },
  // Immigration specific fields
  alienNumber: String,
  alienNumberRequired: String,
  contractValue: {
    type: Number,
    default: 0
  },
  contractDate: Date,
  dateOfContract: Date,
  importantDeadlines: String,
  deadlineDescription: String,
  paymentPlan: String,
  // Family law specific fields
  otherParent: {
    name: String,
    address: String,
    email: String,
    phone: String,
    birthday: Date,
    employer: String,
    employerAddress: String,
    monthlyIncome: Number,
    jobTitle: String,
    education: String,
    addressDuration: String,
    household: String
  },
  children: [{
    name: String,
    gender: String,
    birthdate: Date,
    medicalIssues: String,
    birthPlace: String
  }],
  custody: {
    primaryWith: String,
    idealSchedule: String,
    agreementExists: Boolean,
    supportAgreement: Boolean,
    livingPlaces: String,
    schoolAgreement: Boolean,
    currentSchools: String
  },
  insurance: {
    carrier: String,
    policyNumber: String,
    adjuster: {
      name: String,
      phone: String,
      email: String
    },
    claimNumber: String,
    healthInsurance: String,
    paymentBy: String,
    monthlyCost: Number
  },
  legal: {
    otherProceedings: String,
    cpsInvestigation: String,
    criminalRecord: String,
    unpaidSupport: String,
    otherAttorney: String
  },
  mentalHealth: {
    concerns: String,
    therapistInfo: String,
    parentingConcerns: String,
    behaviorConcerns: String
  },
  // Additional backup fields
  office: String,
  createdBy: String,
  mycaseId: String,
  contacts: String,
  // Structured contacts array
  contactsList: [{
    name: String,
    firstName: String,
    lastName: String,
    email: String,
    phone: String,
    jobTitle: String,
    homeAddress: String,
    specialNotes: String,
    type: {
      type: String,
      enum: ['client', 'attorney', 'case_manager', 'other'],
      default: 'client'
    }
  }],
  billingType: String,
  billingContact: String,
  conflictCheck: Boolean,
  conflictCheckNotes: String,
  linkId: String,
  customField7: String,
  dueDate: Date,
  originatingAttorney: String,
  originatingAttorneyName: String,
  leadAttorneyName: String,
  clientAlternateNames: [String],
  // Financial tracking - separated categories
  earnedAttorneyFee: {
    type: Number,
    default: 0
  },
  paidFee: {
    type: Number,
    default: 0
  },
  caseBalance: {
    type: Number,
    default: 0
  },
  // Detailed financial breakdown
  financials: {
    totalBilled: {
      type: Number,
      default: 0
    },
    totalPaid: {
      type: Number,
      default: 0
    },
    remainingBalance: {
      type: Number,
      default: 0
    },
    expenses: {
      type: Number,
      default: 0
    },
    retainerAmount: {
      type: Number,
      default: 0
    },
    retainerBalance: {
      type: Number,
      default: 0
    }
  },
  description: {
    type: String,
    required: false
  },
  status: {
    type: String,
    enum: ['active', 'closed', 'on_hold', 'pending', 'open'],
    default: 'active'
  },
  attorney: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  caseManager: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  documents: [{
    filename: String,
    originalName: String,
    path: String,
    mimetype: String,
    size: Number,
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    category: {
      type: String,
      enum: ['contract', 'evidence', 'correspondence', 'court_filing', 'other'],
      default: 'other'
    }
  }],
  importantDates: [{
    title: String,
    date: Date,
    description: String,
    type: {
      type: String,
      enum: ['court_date', 'deadline', 'meeting', 'merit_hearing', 'call_up_date', 'other'],
      default: 'other'
    }
  }],
  notes: [{
    content: String,
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  billingRate: {
    type: Number,
    default: 0
  },
  totalBilled: {
    type: Number,
    default: 0
  },
  totalPaid: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

caseSchema.pre('save', async function(next) {
  if (!this.caseNumber) {
    const count = await mongoose.model('Case').countDocuments();
    this.caseNumber = `CASE-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

module.exports = mongoose.model('Case', caseSchema);