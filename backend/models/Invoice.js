const mongoose = require('mongoose');

const invoiceSchema = new mongoose.Schema({
  invoiceNumber: {
    type: String,
    required: true,
    unique: true
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  invoiceDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  billingUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  clientAddress: {
    street: String,
    street2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  fromDate: Date,
  toDate: Date,
  paymentTerms: {
    type: String,
    default: 'Net 30'
  },
  termsAndConditions: String,
  notes: String,
  status: {
    type: String,
    enum: ['draft', 'sent', 'paid', 'overdue', 'cancelled'],
    default: 'draft'
  },
  // Financial totals
  timeEntryTotal: {
    type: Number,
    default: 0
  },
  expenseTotal: {
    type: Number,
    default: 0
  },
  flatFeeTotal: {
    type: Number,
    default: 0
  },
  subtotal: {
    type: Number,
    default: 0
  },
  discountTotal: {
    type: Number,
    default: 0
  },
  writeOffTotal: {
    type: Number,
    default: 0
  },
  additionTotal: {
    type: Number,
    default: 0
  },
  balanceForwardTotal: {
    type: Number,
    default: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paidAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  balanceDue: {
    type: Number,
    default: 0
  },
  // Line items
  timeEntries: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TimeEntry'
  }],
  expenses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Expense'
  }],
  flatFees: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FlatFee'
  }],
  // Payment tracking
  payments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Payment'
  }],
  paidDate: Date,
  // Settings
  allowOnlinePayments: {
    type: Boolean,
    default: true
  },
  sent: {
    type: Boolean,
    default: false
  },
  sentDate: Date,
  // Sharing and access
  sharedWith: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  archived: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  mycase_id: String // For migration tracking
}, {
  timestamps: true
});

// Auto-generate invoice number
invoiceSchema.pre('save', async function(next) {
  if (!this.invoiceNumber) {
    const count = await mongoose.model('Invoice').countDocuments();
    this.invoiceNumber = `INV-${String(count + 1).padStart(6, '0')}`;
  }
  
  // Calculate totals
  this.subtotal = this.timeEntryTotal + this.expenseTotal + this.flatFeeTotal;
  this.totalAmount = this.subtotal - this.discountTotal - this.writeOffTotal + this.additionTotal + this.balanceForwardTotal;
  this.balanceDue = this.totalAmount - this.paidAmount;
  
  next();
});

module.exports = mongoose.model('Invoice', invoiceSchema);