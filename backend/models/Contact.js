const mongoose = require('mongoose');

const contactSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  middleName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  company: {
    type: String,
    trim: true
  },
  jobTitle: {
    type: String,
    trim: true
  },
  // Contact Information
  homeAddress: {
    street: String,
    street2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  workAddress: {
    street: String,
    street2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  phones: {
    home: String,
    work: String,
    mobile: String,
    fax: String
  },
  email: {
    type: String,
    lowercase: true,
    trim: true
  },
  webPage: String,
  // Personal Information
  birthday: Date,
  ssn: String, // Encrypted
  driversLicense: String,
  placeOfBirth: String,
  // Professional Information
  licenseNumber: String,
  licenseState: String,
  // Financial Information
  outstandingTrustBalance: {
    type: Number,
    default: 0
  },
  nonTrustCreditBalance: {
    type: Number,
    default: 0
  },
  // System fields
  contactGroup: String,
  loginEnabled: {
    type: Boolean,
    default: false
  },
  archived: {
    type: Boolean,
    default: false
  },
  welcomeMessage: String,
  privateNotes: String,
  // Related data
  cases: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case'
  }],
  // Custom fields for family law cases
  customFields: {
    spouseInfo: {
      fullName: String,
      dateOfBirth: Date,
      placeOfBirth: String,
      address: String,
      phoneNumber: String,
      email: String,
      employer: String,
      monthlyIncome: Number
    },
    householdInfo: {
      whoLivesWithYou: String,
      whoLivesWithSpouse: String
    },
    employmentInfo: {
      employer: String,
      jobTitle: String,
      address: String,
      phoneNumber: String,
      monthlySalary: Number
    },
    // Immigration specific
    alienNumber: String,
    // Divorce specific fields
    dateOfMarriage: Date,
    dateOfSeparation: Date,
    reasonForDivorce: String,
    county: String
  },
  mycase_id: String // For migration tracking
}, {
  timestamps: true
});

// Virtual for full name
contactSchema.virtual('fullName').get(function() {
  const parts = [this.firstName, this.middleName, this.lastName].filter(part => part);
  return parts.join(' ');
});

module.exports = mongoose.model('Contact', contactSchema);