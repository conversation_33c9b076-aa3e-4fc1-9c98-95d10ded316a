const mongoose = require('mongoose');

const flatFeeSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  enteredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  invoiceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice'
  },
  nonbillable: {
    type: Boolean,
    default: false
  },
  billed: {
    type: Boolean,
    default: false
  },
  category: {
    type: String,
    enum: ['consultation', 'document_prep', 'court_appearance', 'research', 'filing', 'other'],
    default: 'other'
  },
  mycase_id: String // For migration tracking
}, {
  timestamps: true
});

module.exports = mongoose.model('FlatFee', flatFeeSchema);