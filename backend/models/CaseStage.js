const mongoose = require('mongoose');

const caseStageSchema = new mongoose.Schema({
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  stageName: {
    type: String,
    required: true,
    trim: true
  },
  stageBeginningDate: {
    type: Date,
    required: true
  },
  stageEndingDate: {
    type: Date
  },
  daysSpentInStage: {
    type: Number,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  notes: String,
  completionPercentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  milestones: [{
    name: String,
    completed: {
      type: Boolean,
      default: false
    },
    completedDate: Date,
    completedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }]
}, {
  timestamps: true
});

// Calculate days spent in stage
caseStageSchema.pre('save', function(next) {
  if (this.stageBeginningDate && this.stageEndingDate) {
    const timeDiff = this.stageEndingDate.getTime() - this.stageBeginningDate.getTime();
    this.daysSpentInStage = Math.ceil(timeDiff / (1000 * 3600 * 24));
  }
  next();
});

module.exports = mongoose.model('CaseStage', caseStageSchema);