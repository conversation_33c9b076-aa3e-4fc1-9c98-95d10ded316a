const mongoose = require('mongoose');

const expenseSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true
  },
  activity: {
    type: String,
    required: true,
    trim: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
    default: 1
  },
  cost: {
    type: Number,
    required: true,
    min: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  invoiceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice'
  },
  nonbillable: {
    type: Boolean,
    default: false
  },
  billed: {
    type: Boolean,
    default: false
  },
  category: {
    type: String,
    enum: ['travel', 'filing_fees', 'copying', 'postage', 'research', 'expert_fees', 'other'],
    default: 'other'
  },
  receiptUrl: String,
  mycase_id: String // For migration tracking
}, {
  timestamps: true
});

// Calculate total before saving
expenseSchema.pre('save', function(next) {
  this.total = this.quantity * this.cost;
  next();
});

module.exports = mongoose.model('Expense', expenseSchema);