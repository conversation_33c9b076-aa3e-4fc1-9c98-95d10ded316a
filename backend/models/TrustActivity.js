const mongoose = require('mongoose');

const trustActivitySchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true
  },
  relatedTo: {
    type: String,
    required: true,
    trim: true
  },
  contact: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Contact'
  },
  caseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Case',
    required: true
  },
  enteredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  originatingAttorney: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  leadAttorney: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: String,
  paymentNotes: String,
  paymentMethod: {
    type: String,
    enum: ['cash', 'check', 'credit_card', 'bank_transfer', 'wire', 'zelle', 'other'],
    default: 'other'
  },
  // Transaction details
  amount: {
    type: Number,
    required: true
  },
  transactionType: {
    type: String,
    enum: ['deposit', 'withdrawal', 'transfer', 'adjustment'],
    required: true
  },
  // Trust account specifics
  isTrustPayment: {
    type: Boolean,
    default: true
  },
  trustBalance: {
    type: Number,
    default: 0
  },
  // Refund tracking
  isRefund: {
    type: Boolean,
    default: false
  },
  refunded: {
    type: Boolean,
    default: false
  },
  refundDate: Date,
  refundReason: String,
  // Rejection tracking
  isRejection: {
    type: Boolean,
    default: false
  },
  rejected: {
    type: Boolean,
    default: false
  },
  rejectionDate: Date,
  rejectionReason: String,
  // Credit tracking
  isCredit: {
    type: Boolean,
    default: false
  },
  operatingCredit: {
    type: Number,
    default: 0
  },
  // Running totals
  runningTrustBalance: {
    type: Number,
    default: 0
  },
  runningOperatingBalance: {
    type: Number,
    default: 0
  },
  mycase_id: String // For migration tracking
}, {
  timestamps: true
});

// Ensure proper trust accounting compliance
trustActivitySchema.pre('save', function(next) {
  // Trust accounting rules validation
  if (this.isTrustPayment && this.transactionType === 'withdrawal' && this.trustBalance < 0) {
    return next(new Error('Trust account cannot have negative balance'));
  }
  next();
});

module.exports = mongoose.model('TrustActivity', trustActivitySchema);