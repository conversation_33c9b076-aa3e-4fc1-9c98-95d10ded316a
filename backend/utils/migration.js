const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Import all models
const Case = require('../models/Case');
const User = require('../models/User');
const Contact = require('../models/Contact');
const TimeEntry = require('../models/TimeEntry');
const Expense = require('../models/Expense');
const FlatFee = require('../models/FlatFee');
const Task = require('../models/Task');
const Invoice = require('../models/Invoice');
const CaseStage = require('../models/CaseStage');
const TrustActivity = require('../models/TrustActivity');

class MigrationUtility {
  constructor(backupPath) {
    this.backupPath = backupPath;
    this.csvFiles = {};
    this.migrationLog = [];
    this.errors = [];
    this.stats = {
      processed: 0,
      imported: 0,
      skipped: 0,
      errors: 0
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    this.migrationLog.push(logEntry);
    console.log(logEntry);
  }

  async loadCSVFiles() {
    this.log('Loading CSV files from backup...');
    const files = fs.readdirSync(this.backupPath);
    
    for (const file of files) {
      if (file.endsWith('.csv')) {
        const filePath = path.join(this.backupPath, file);
        this.csvFiles[file] = filePath;
        this.log(`Found CSV file: ${file}`);
      }
    }
    
    this.log(`Total CSV files found: ${Object.keys(this.csvFiles).length}`);
  }

  async readCSV(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv({
          skipEmptyLines: true,
          skipLinesWithError: true,
          headers: true
        }))
        .on('data', (data) => {
          // Clean up field names and values
          const cleanData = {};
          Object.keys(data).forEach(key => {
            const cleanKey = key.trim();
            const cleanValue = data[key] ? data[key].toString().trim() : '';
            cleanData[cleanKey] = cleanValue === 'NULL' || cleanValue === '' ? null : cleanValue;
          });
          results.push(cleanData);
        })
        .on('end', () => resolve(results))
        .on('error', (error) => {
          console.log(`CSV parsing error in ${filePath}: ${error.message}`);
          resolve(results); // Return what we have so far
        });
    });
  }

  parseDate(dateStr) {
    if (!dateStr || dateStr === 'NULL' || dateStr === '') return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  parseNumber(numStr) {
    if (!numStr || numStr === 'NULL' || numStr === '') return 0;
    const num = parseFloat(numStr);
    return isNaN(num) ? 0 : num;
  }

  parseBoolean(boolStr) {
    if (!boolStr || boolStr === 'NULL') return false;
    return ['1', 'true', 'yes', 'on'].includes(boolStr.toLowerCase());
  }

  async migrateLawyers() {
    this.log('Starting lawyers migration...');
    const lawyersFile = this.csvFiles['lawyers.csv'];
    if (!lawyersFile) {
      this.log('No lawyers.csv file found', 'warning');
      return;
    }

    const lawyers = await this.readCSV(lawyersFile);
    this.log(`Processing ${lawyers.length} lawyers...`);

    for (const lawyer of lawyers) {
      try {
        this.stats.processed++;
        
        // Skip if no first name or last name
        if (!lawyer['First name'] && !lawyer['Last name']) {
          this.stats.skipped++;
          continue;
        }
        
        // Check if user already exists
        const existingUser = await User.findOne({ email: lawyer['Email'] });
        if (existingUser) {
          this.stats.skipped++;
          continue;
        }

        const hashedPassword = await bcrypt.hash('password123', 10);
        
        const newUser = new User({
          firstName: lawyer['First name'] || '',
          lastName: lawyer['Last name'] || '',
          name: `${lawyer['First name'] || ''} ${lawyer['Last name'] || ''}`.trim(),
          email: lawyer['Email'] || `user${lawyer['MyCase ID']}@migrated.local`,
          password: hashedPassword,
          role: this.mapUserType(lawyer['User Type']),
          isActive: !this.parseBoolean(lawyer['Archived']),
          phone: lawyer['Cell phone'] || lawyer['Work phone'] || lawyer['Home phone'],
          address: {
            street: lawyer['Street'],
            city: lawyer['City'],
            state: lawyer['State'],
            zipCode: lawyer['Postal Code']
          },
          hourlyRate: this.parseNumber(lawyer['Default rate'])
        });

        await newUser.save();
        this.stats.imported++;
        
      } catch (error) {
        this.stats.errors++;
        this.errors.push(`Lawyer migration error (ID: ${lawyer['MyCase ID']}): ${error.message}`);
      }
    }

    this.log(`Lawyers migration completed: ${this.stats.imported} imported, ${this.stats.skipped} skipped, ${this.stats.errors} errors`);
  }

  async migrateContacts() {
    this.log('Starting contacts migration...');
    const contactsFile = this.csvFiles['people.csv']; // Use people.csv instead
    if (!contactsFile) {
      this.log('No people.csv file found', 'warning');
      return;
    }

    const contacts = await this.readCSV(contactsFile);
    this.log(`Processing ${contacts.length} contacts...`);

    for (const contact of contacts) {
      try {
        this.stats.processed++;
        
        // Skip if no name
        if (!contact['First Name'] && !contact['Last Name'] && !contact['Company']) {
          this.stats.skipped++;
          continue;
        }
        
        // Check if contact already exists
        const existingContact = await Contact.findOne({ 
          $or: [
            { email: contact['E-mail Address'] },
            { phone: contact['Mobile Phone'] || contact['Home Phone'] || contact['Work Phone'] }
          ]
        });
        
        if (existingContact && contact['E-mail Address']) {
          this.stats.skipped++;
          continue;
        }

        const newContact = new Contact({
          type: contact['Company'] ? 'business' : 'individual',
          firstName: contact['First Name'],
          lastName: contact['Last Name'],
          companyName: contact['Company'],
          email: contact['E-mail Address'],
          phone: contact['Mobile Phone'] || contact['Home Phone'] || contact['Work Phone'],
          mobile: contact['Mobile Phone'],
          fax: contact['Home Fax'],
          address: {
            street: contact['Home Street'],
            city: contact['Home City'],
            state: contact['Home State'],
            zipCode: contact['Home Postal Code'],
            country: contact['Home Country/Region'] || 'USA'
          },
          notes: contact['Private Notes'] || contact[':Notes'],
          isActive: !this.parseBoolean(contact['Archived']),
          createdAt: this.parseDate(contact['Created Date']) || new Date()
        });

        await newContact.save();
        this.stats.imported++;
        
      } catch (error) {
        this.stats.errors++;
        this.errors.push(`Contact migration error (ID: ${contact['MyCase ID']}): ${error.message}`);
      }
    }

    this.log(`Contacts migration completed: ${this.stats.imported} imported, ${this.stats.skipped} skipped, ${this.stats.errors} errors`);
  }

  async migrateCases() {
    this.log('Starting cases migration...');
    const casesFile = this.csvFiles['cases.csv'];
    if (!casesFile) {
      this.log('No cases.csv file found', 'warning');
      return;
    }

    const cases = await this.readCSV(casesFile);
    this.log(`Processing ${cases.length} cases...`);

    // Get default attorney for cases without one
    const defaultAttorney = await User.findOne({ role: 'attorney' });
    if (!defaultAttorney) {
      this.log('No attorney found in system for case assignment', 'error');
      return;
    }

    for (const caseData of cases) {
      try {
        this.stats.processed++;
        
        // Check if case already exists
        const existingCase = await Case.findOne({ 
          $or: [
            { caseNumber: caseData['Number'] },
            { mycaseId: caseData['MyCase ID'] }
          ]
        });
        if (existingCase) {
          this.stats.skipped++;
          continue;
        }

        // Find attorney by name or use default
        let attorney = defaultAttorney;
        const leadAttorneyName = caseData['Lead Attorney'] || caseData['Lead Attorney Name'];
        if (leadAttorneyName) {
          const foundAttorney = await User.findOne({ 
            $or: [
              { name: new RegExp(leadAttorneyName, 'i') },
              { firstName: new RegExp(leadAttorneyName.split(' ')[0] || '', 'i') },
              { lastName: new RegExp(leadAttorneyName.split(' ').slice(-1)[0] || '', 'i') }
            ]
          });
          if (foundAttorney) attorney = foundAttorney;
        }

        const newCase = new Case({
          caseNumber: caseData['Number'] || `MIGRATED-${caseData['MyCase ID']}`,
          caseName: caseData['Case/Matter Name'] || `Case ${caseData['MyCase ID']}`,
          clientName: caseData['CONTACT NAME'] || caseData['Client Name'] || 'Unknown Client',
          clientEmail: `client${caseData['MyCase ID']}@migrated.local`,
          clientPhone: caseData['Phone No.'] || 'N/A',
          caseType: this.mapCaseType(caseData['Practice Area']),
          practiceArea: caseData['Practice Area'],
          description: caseData['Case Description'] || 'Migrated case',
          status: this.parseBoolean(caseData['Case Closed']) || this.parseBoolean(caseData['Closed']) ? 'closed' : 'active',
          attorney: attorney._id,
          openDate: this.parseDate(caseData['Open Date']) || new Date(),
          closedDate: this.parseDate(caseData['Closed Date']),
          solDate: this.parseDate(caseData['SOL Date']),
          referralSource: caseData['Where clients come from'],
          flatFee: this.parseNumber(caseData['Flat fee']),
          
          // Enhanced financial tracking
          retainer: {
            amount: this.parseNumber(caseData['Retainer']),
            received: this.parseNumber(caseData['Paid Fee']),
            balance: this.parseNumber(caseData['Outstanding Balance'])
          },
          totalBilled: this.parseNumber(caseData['Case Balance']),
          totalPaid: this.parseNumber(caseData['Earned Attorney Fee']),
          earnedAttorneyFee: this.parseNumber(caseData['Earned Attorney Fee']),
          paidFee: this.parseNumber(caseData['Paid Fee']),
          caseBalance: this.parseNumber(caseData['Case Balance']),
          contractValue: this.parseNumber(caseData['CONTRACT VALUE']),
          
          // Administrative fields
          office: caseData['Office'],
          createdBy: caseData['Created By'],
          mycaseId: caseData['MyCase ID'],
          contacts: caseData['Contacts'],
          billingType: caseData['Billing Type'],
          billingContact: caseData['Billing Contact'],
          conflictCheck: this.parseBoolean(caseData['Conflict Check?']),
          conflictCheckNotes: caseData['Conflict Check Notes'],
          linkId: caseData['Link ID'],
          customField7: caseData['Custom Field 7'],
          dueDate: this.parseDate(caseData['Due Date']),
          originatingAttorney: caseData['Originating Attorney'],
          originatingAttorneyName: caseData['Originating Attorney Name'],
          leadAttorneyName: caseData['Lead Attorney Name'],
          paymentPlan: caseData['Paym.Plan '],
          
          // Immigration specific
          alienNumber: caseData['Alien Number'],
          alienNumberRequired: caseData['Alien Number (required for court cases)'],
          contractDate: this.parseDate(caseData['DATE OF CONTRACT SIGNED']),
          dateOfContract: this.parseDate(caseData['Date of K']),
          importantDeadlines: caseData['IMPORTANT DEADLINES'],
          deadlineDescription: caseData['DEADLINE DESCRIPTION'],
          
          // Family law specific - Other Parent info
          otherParent: {
            name: caseData["Other Parent's Name"],
            address: caseData["Other Parent's Address"],
            email: caseData["Other Parent's Email Address"],
            phone: caseData["Other Parent's Phone Number"],
            birthday: this.parseDate(caseData["Other Parent's Birthday"]),
            employer: caseData["Other Parent's Employer"],
            employerAddress: caseData["Other Parent's Employer's Address"],
            monthlyIncome: this.parseNumber(caseData["Other Parent's Gross Monthly Income"]),
            jobTitle: caseData["Other Parent's Job Title"],
            education: caseData["Does the other parent have any special education, training or licenses?"],
            addressDuration: caseData["How long has the other parent lived at this address?"],
            household: caseData["Who else lives in the household with the other parent?"]
          },
          
          // Custody and children info
          custody: {
            primaryWith: caseData["Who do the children live with primarily?"],
            idealSchedule: caseData["What would be your ideal custody and visitation schedule for the child(ren)?"],
            agreementExists: this.parseBoolean(caseData["Do you and the other parent agree on a custody schedule?"]),
            supportAgreement: this.parseBoolean(caseData["Do you and the other parent agree on a monthly amount for child support?"]),
            livingPlaces: caseData["Please provide a list of the places where the child(ren) have lived for the past five years."],
            schoolAgreement: this.parseBoolean(caseData["Do you and the other parent agree on where to send the kids to school?"]),
            currentSchools: caseData["What school(s) do the child(ren) currently attend? Please list all schools attended since Kindergarten."]
          },
          
          // Insurance and medical
          insurance: {
            healthInsurance: caseData["Do you have health insurance and/or dental insurance for the child(ren)? What company or agency provides the health/dental insurance?"],
            paymentBy: caseData["Who pays for health insurance/dental insurance for the child(ren)?"],
            monthlyCost: this.parseNumber(caseData["What is the monthly cost to insure the child(ren)?"])
          },
          
          // Legal issues
          legal: {
            otherProceedings: caseData["Are there any other court proceedings happening right now? Examples include criminal court cases, protective orders, Office of the Attorney General child support cases, etc."],
            cpsInvestigation: caseData["Has either parent ever been involved in a CPS investigation? Or a police investigation concerning child abuse or neglect? Please provide details."],
            criminalRecord: caseData["Have you or the other parent ever been arrested or charged with a crime (other than a traffic ticket)? Please provide details."],
            unpaidSupport: caseData["Does the other party owe unpaid child support? If so, how much?"],
            otherAttorney: caseData["Does the other parent have an attorney? Do you think s/he will hire one?"]
          },
          
          // Mental health and parenting
          mentalHealth: {
            concerns: caseData["Please detail any mental health concerns for you, the other parent, or the children."],
            therapistInfo: caseData["Do you or your child(ren) see a therapist or school counselor? Please list the names and contact information for any mental health professionals."],
            parentingConcerns: caseData["Please list any concerns you have about the other parent's behavior or parenting."],
            behaviorConcerns: caseData["Please list any concerns you think that the other parent has about your behavior or parenting."]
          },
          
          // Additional fields
          archived: false,
          clientType: 'individual',
          priority: 'normal'
        });

        await newCase.save();
        this.stats.imported++;
        
      } catch (error) {
        this.stats.errors++;
        this.errors.push(`Case migration error (ID: ${caseData.id}): ${error.message}`);
      }
    }

    this.log(`Cases migration completed: ${this.stats.imported} imported, ${this.stats.skipped} skipped, ${this.stats.errors} errors`);
  }

  async migrateTimeEntries() {
    this.log('Starting time entries migration...');
    const timeFile = this.csvFiles['time_entries.csv'];
    if (!timeFile) {
      this.log('No time_entries.csv file found', 'warning');
      return;
    }

    const timeEntries = await this.readCSV(timeFile);
    this.log(`Processing ${timeEntries.length} time entries...`);

    for (const timeEntry of timeEntries) {
      try {
        this.stats.processed++;
        
        // Find related case and user
        const relatedCase = await Case.findOne({ caseNumber: timeEntry.case_number });
        const user = await User.findOne({ email: timeEntry.user_email });
        
        if (!relatedCase) {
          this.stats.skipped++;
          continue;
        }

        const newTimeEntry = new TimeEntry({
          case: relatedCase._id,
          user: user ? user._id : relatedCase.attorney,
          date: this.parseDate(timeEntry.date) || new Date(),
          hours: this.parseNumber(timeEntry.hours),
          description: timeEntry.description || 'Migrated time entry',
          rate: this.parseNumber(timeEntry.rate),
          amount: this.parseNumber(timeEntry.amount),
          billable: this.parseBoolean(timeEntry.billable),
          billed: this.parseBoolean(timeEntry.billed),
          category: timeEntry.category || 'general'
        });

        await newTimeEntry.save();
        this.stats.imported++;
        
      } catch (error) {
        this.stats.errors++;
        this.errors.push(`Time entry migration error (ID: ${timeEntry.id}): ${error.message}`);
      }
    }

    this.log(`Time entries migration completed: ${this.stats.imported} imported, ${this.stats.skipped} skipped, ${this.stats.errors} errors`);
  }

  async migrateExpenses() {
    this.log('Starting expenses migration...');
    const expensesFile = this.csvFiles['expenses.csv'];
    if (!expensesFile) {
      this.log('No expenses.csv file found', 'warning');
      return;
    }

    const expenses = await this.readCSV(expensesFile);
    this.log(`Processing ${expenses.length} expenses...`);

    for (const expense of expenses) {
      try {
        this.stats.processed++;
        
        // Find related case and user
        const relatedCase = await Case.findOne({ caseNumber: expense.case_number });
        const user = await User.findOne({ email: expense.user_email });
        
        if (!relatedCase) {
          this.stats.skipped++;
          continue;
        }

        const newExpense = new Expense({
          case: relatedCase._id,
          user: user ? user._id : relatedCase.attorney,
          date: this.parseDate(expense.date) || new Date(),
          amount: this.parseNumber(expense.amount),
          description: expense.description || 'Migrated expense',
          category: expense.category || 'other',
          vendor: expense.vendor,
          receipt: expense.receipt_path,
          billable: this.parseBoolean(expense.billable),
          billed: this.parseBoolean(expense.billed),
          reimbursable: this.parseBoolean(expense.reimbursable),
          reimbursed: this.parseBoolean(expense.reimbursed)
        });

        await newExpense.save();
        this.stats.imported++;
        
      } catch (error) {
        this.stats.errors++;
        this.errors.push(`Expense migration error (ID: ${expense.id}): ${error.message}`);
      }
    }

    this.log(`Expenses migration completed: ${this.stats.imported} imported, ${this.stats.skipped} skipped, ${this.stats.errors} errors`);
  }

  async migrateTasks() {
    this.log('Starting tasks migration...');
    const tasksFile = this.csvFiles['tasks.csv'];
    if (!tasksFile) {
      this.log('No tasks.csv file found', 'warning');
      return;
    }

    const tasks = await this.readCSV(tasksFile);
    this.log(`Processing ${tasks.length} tasks...`);

    for (const task of tasks) {
      try {
        this.stats.processed++;
        
        // Find related case and users
        const relatedCase = await Case.findOne({ caseNumber: task.case_number });
        const assignedTo = await User.findOne({ email: task.assigned_to_email });
        const createdBy = await User.findOne({ email: task.created_by_email });
        
        const newTask = new Task({
          title: task.title || 'Migrated task',
          description: task.description,
          case: relatedCase ? relatedCase._id : null,
          assignedTo: assignedTo ? assignedTo._id : null,
          createdBy: createdBy ? createdBy._id : null,
          status: this.mapTaskStatus(task.status),
          priority: this.mapPriority(task.priority),
          dueDate: this.parseDate(task.due_date),
          completedAt: this.parseDate(task.completed_at),
          estimatedHours: this.parseNumber(task.estimated_hours),
          actualHours: this.parseNumber(task.actual_hours),
          category: task.category || 'general',
          tags: task.tags ? task.tags.split(',').map(t => t.trim()) : []
        });

        await newTask.save();
        this.stats.imported++;
        
      } catch (error) {
        this.stats.errors++;
        this.errors.push(`Task migration error (ID: ${task.id}): ${error.message}`);
      }
    }

    this.log(`Tasks migration completed: ${this.stats.imported} imported, ${this.stats.skipped} skipped, ${this.stats.errors} errors`);
  }

  // Helper methods for mapping values
  mapUserType(userType) {
    if (!userType) return 'attorney';
    const type = userType.toLowerCase();
    if (type.includes('admin')) return 'admin';
    if (type.includes('paralegal')) return 'paralegal';
    if (type.includes('secretary')) return 'secretary';
    return 'attorney';
  }

  mapCaseType(type) {
    const typeMap = {
      'personal injury': 'personal_injury',
      'criminal': 'criminal_defense',
      'family': 'family_law',
      'business': 'business_law',
      'estate': 'estate_planning',
      'immigration': 'immigration'
    };
    return typeMap[type?.toLowerCase()] || 'other';
  }

  mapCaseStatus(status) {
    const statusMap = {
      'open': 'active',
      'active': 'active',
      'closed': 'closed',
      'pending': 'pending',
      'hold': 'on_hold'
    };
    return statusMap[status?.toLowerCase()] || 'active';
  }

  mapPriority(priority) {
    const priorityMap = {
      '1': 'urgent',
      '2': 'high',
      '3': 'normal',
      '4': 'low',
      'urgent': 'urgent',
      'high': 'high',
      'medium': 'normal',
      'normal': 'normal',
      'low': 'low'
    };
    return priorityMap[priority?.toLowerCase()] || 'normal';
  }

  mapTaskStatus(status) {
    const statusMap = {
      'open': 'pending',
      'pending': 'pending',
      'in_progress': 'in_progress',
      'completed': 'completed',
      'cancelled': 'cancelled'
    };
    return statusMap[status?.toLowerCase()] || 'pending';
  }

  async runFullMigration() {
    this.log('Starting full migration process...');
    
    try {
      await this.loadCSVFiles();
      
      // Reset stats for full migration
      this.stats = { processed: 0, imported: 0, skipped: 0, errors: 0 };
      
      // Run migrations in order of dependencies
      await this.migrateLawyers();
      await this.migrateContacts();
      await this.migrateCases();
      await this.migrateTimeEntries();
      await this.migrateExpenses();
      await this.migrateTasks();
      
      this.log('Full migration completed!');
      this.log(`Total stats - Processed: ${this.stats.processed}, Imported: ${this.stats.imported}, Skipped: ${this.stats.skipped}, Errors: ${this.stats.errors}`);
      
      if (this.errors.length > 0) {
        this.log('Migration completed with errors:', 'warning');
        this.errors.forEach(error => this.log(error, 'error'));
      }
      
      return {
        success: true,
        stats: this.stats,
        errors: this.errors,
        log: this.migrationLog
      };
      
    } catch (error) {
      this.log(`Migration failed: ${error.message}`, 'error');
      return {
        success: false,
        error: error.message,
        stats: this.stats,
        errors: this.errors,
        log: this.migrationLog
      };
    }
  }
}

module.exports = MigrationUtility;