const puppeteer = require('puppeteer');

class PDFGenerator {
  static async generateInvoicePDF(invoice) {
    try {
      const browser = await puppeteer.launch({ 
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const page = await browser.newPage();
      
      // Generate HTML content for the invoice
      const htmlContent = generateInvoiceHTML(invoice);
      
      await page.setContent(htmlContent);
      
      // Generate PDF
      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm'
        }
      });
      
      await browser.close();
      
      return pdf;
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }
}

function generateInvoiceHTML(invoice) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Invoice #${invoice.invoiceNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .company-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .invoice-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .client-info, .invoice-details {
          flex: 1;
        }
        .invoice-details {
          text-align: right;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .items-table th, .items-table td {
          border: 1px solid #ddd;
          padding: 12px;
          text-align: left;
        }
        .items-table th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        .total-section {
          text-align: right;
          margin-top: 20px;
        }
        .total-row {
          margin: 10px 0;
          font-size: 16px;
        }
        .grand-total {
          font-size: 20px;
          font-weight: bold;
          border-top: 2px solid #333;
          padding-top: 10px;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 12px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-name">Durian Law Firm</div>
        <div>Professional Legal Services</div>
      </div>
      
      <div class="invoice-info">
        <div class="client-info">
          <h3>Bill To:</h3>
          <p><strong>${invoice.caseId?.clientName || 'Client Name'}</strong></p>
          <p>Case: ${invoice.caseId?.caseNumber || 'N/A'}</p>
          <p>${invoice.caseId?.caseName || 'Case Description'}</p>
        </div>
        
        <div class="invoice-details">
          <h3>Invoice Details:</h3>
          <p><strong>Invoice #:</strong> ${invoice.invoiceNumber}</p>
          <p><strong>Date:</strong> ${formatDate(invoice.invoiceDate)}</p>
          <p><strong>Due Date:</strong> ${formatDate(invoice.dueDate)}</p>
          <p><strong>Status:</strong> ${invoice.status}</p>
        </div>
      </div>
      
      <table class="items-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Date</th>
            <th>Hours</th>
            <th>Rate</th>
            <th>Amount</th>
          </tr>
        </thead>
        <tbody>
          ${invoice.timeEntries?.map(entry => `
            <tr>
              <td>${entry.description || 'Legal Services'}</td>
              <td>${formatDate(entry.date)}</td>
              <td>${entry.hours || 0}</td>
              <td>${formatCurrency(entry.rate || 0)}</td>
              <td>${formatCurrency(entry.amount || 0)}</td>
            </tr>
          `).join('') || '<tr><td colspan="5">No time entries</td></tr>'}
        </tbody>
      </table>
      
      <div class="total-section">
        <div class="total-row">
          <strong>Subtotal:</strong> ${formatCurrency(invoice.subtotal)}
        </div>
        <div class="total-row">
          <strong>Tax:</strong> ${formatCurrency(invoice.tax)}
        </div>
        <div class="total-row">
          <strong>Discount:</strong> ${formatCurrency(invoice.discount)}
        </div>
        <div class="total-row grand-total">
          <strong>Total Amount:</strong> ${formatCurrency(invoice.totalAmount)}
        </div>
        <div class="total-row">
          <strong>Paid Amount:</strong> ${formatCurrency(invoice.paidAmount)}
        </div>
        <div class="total-row">
          <strong>Balance Due:</strong> ${formatCurrency(invoice.balanceDue)}
        </div>
      </div>
      
      <div class="footer">
        <p>Thank you for your business. Please make checks payable to Durian Law Firm.</p>
        <p>For questions about this invoice, please contact our billing department.</p>
      </div>
    </body>
    </html>
  `;
}

module.exports = PDFGenerator;
