const express = require('express');
const router = express.Router();
const TimeEntry = require('../models/TimeEntry');
const Case = require('../models/Case');
const { auth } = require('../middleware/auth');
const { cleanObjectIds, validateRequiredFields } = require('../middleware/validation');

// GET /api/time-entries - Get all time entries with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const userId = req.query.userId;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    const billable = req.query.billable;

    // Build query
    const query = {};
    
    if (caseId) query.caseId = caseId;
    if (userId) query.user = userId;
    if (billable !== undefined) query.nonbillable = !(billable === 'true');
    
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    const timeEntries = await TimeEntry.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName')
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TimeEntry.countDocuments(query);

    // Calculate totals
    const totalHours = await TimeEntry.aggregate([
      { $match: query },
      { $group: { _id: null, totalHours: { $sum: { $divide: ['$timeSpent', 60] } }, totalAmount: { $sum: '$total' } } }
    ]);

    res.json({
      timeEntries,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        totalHours: totalHours[0]?.totalHours || 0,
        totalAmount: totalHours[0]?.totalAmount || 0
      }
    });
  } catch (error) {
    console.error('Error fetching time entries:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/time-entries/:id - Get single time entry
router.get('/:id', auth, async (req, res) => {
  try {
    const timeEntry = await TimeEntry.findById(req.params.id)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName');

    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }

    res.json(timeEntry);
  } catch (error) {
    console.error('Error fetching time entry:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/time-entries - Create new time entry
router.post('/', auth, cleanObjectIds, validateRequiredFields(['timeSpent', 'activity', 'hourlyRate', 'description', 'caseId']), async (req, res) => {
  try {
    const timeEntryData = {
      ...req.body,
      user: req.body.user || req.user.id
    };

    // Calculate total if not provided
    if (!timeEntryData.total && timeEntryData.timeSpent && timeEntryData.hourlyRate) {
      timeEntryData.total = (timeEntryData.timeSpent / 60) * timeEntryData.hourlyRate;
    }

    const timeEntry = new TimeEntry(timeEntryData);
    await timeEntry.save();

    // Populate the response
    await timeEntry.populate('caseId', 'caseNumber caseName clientName');
    await timeEntry.populate('user', 'firstName lastName');

    res.status(201).json(timeEntry);
  } catch (error) {
    console.error('Error creating time entry:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/time-entries/:id - Update time entry
router.put('/:id', auth, cleanObjectIds, async (req, res) => {
  try {
    const updateData = { ...req.body };
    
    // Recalculate total if timeSpent or hourlyRate changed
    if ((updateData.timeSpent || updateData.hourlyRate) && updateData.timeSpent && updateData.hourlyRate) {
      updateData.total = (updateData.timeSpent / 60) * updateData.hourlyRate;
    }

    const timeEntry = await TimeEntry.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('user', 'firstName lastName');

    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }

    res.json(timeEntry);
  } catch (error) {
    console.error('Error updating time entry:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/time-entries/:id - Delete time entry
router.delete('/:id', auth, async (req, res) => {
  try {
    const timeEntry = await TimeEntry.findByIdAndDelete(req.params.id);

    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry deleted successfully' });
    }

    res.json({ message: 'Time entry deleted successfully' });
  } catch (error) {
    console.error('Error deleting time entry:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/time-entries/bulk - Create multiple time entries
router.post('/bulk', auth, async (req, res) => {
  try {
    const { entries } = req.body;
    
    if (!Array.isArray(entries)) {
      return res.status(400).json({ message: 'Entries must be an array' });
    }

    // Add user and calculate amounts
    const processedEntries = entries.map(entry => {
      const processedEntry = {
        ...entry,
        user: entry.user || req.user.id
      };
      
      if (!processedEntry.total && processedEntry.timeSpent && processedEntry.hourlyRate) {
        processedEntry.total = (processedEntry.timeSpent / 60) * processedEntry.hourlyRate;
      }
      
      return processedEntry;
    });

    const timeEntries = await TimeEntry.insertMany(processedEntries);

    // Populate all entries
    await TimeEntry.populate(timeEntries, [
      { path: 'caseId', select: 'caseNumber caseName clientName' },
      { path: 'user', select: 'firstName lastName' }
    ]);

    res.status(201).json({
      message: `${timeEntries.length} time entries created successfully`,
      timeEntries
    });
  } catch (error) {
    console.error('Error creating bulk time entries:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/time-entries/summary - Get time entries summary
router.get('/summary', auth, async (req, res) => {
  try {
    const { caseId, userId, startDate, endDate } = req.query;
    
    const query = {};
    if (caseId) query.caseId = caseId;
    if (userId) query.user = userId;
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    const summary = await TimeEntry.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalEntries: { $sum: 1 },
          totalHours: { $sum: { $divide: ['$timeSpent', 60] } },
          totalAmount: { $sum: '$total' },
          billableHours: { 
            $sum: { 
              $cond: [{ $eq: ['$nonbillable', false] }, { $divide: ['$timeSpent', 60] }, 0] 
            } 
          },
          billableAmount: { 
            $sum: { 
              $cond: [{ $eq: ['$nonbillable', false] }, '$total', 0] 
            } 
          }
        }
      }
    ]);

    res.json(summary[0] || {
      totalEntries: 0,
      totalHours: 0,
      totalAmount: 0,
      billableHours: 0,
      billableAmount: 0
    });
  } catch (error) {
    console.error('Error fetching time entries summary:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/time-entries/case/:caseId - Get time entries for specific case
router.get('/case/:caseId', auth, async (req, res) => {
  try {
    const timeEntries = await TimeEntry.find({ caseId: req.params.caseId })
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName')
      .sort({ date: -1 });

    const summary = await TimeEntry.aggregate([
      { $match: { caseId: req.params.caseId } },
      {
        $group: {
          _id: null,
          totalHours: { $sum: { $divide: ['$timeSpent', 60] } },
          totalAmount: { $sum: '$total' },
          billableHours: { 
            $sum: { 
              $cond: [{ $eq: ['$nonbillable', false] }, { $divide: ['$timeSpent', 60] }, 0] 
            } 
          },
          billableAmount: { 
            $sum: { 
              $cond: [{ $eq: ['$nonbillable', false] }, '$total', 0] 
            } 
          }
        }
      }
    ]);

    res.json({
      timeEntries,
      summary: summary[0] || {
        totalHours: 0,
        totalAmount: 0,
        billableHours: 0,
        billableAmount: 0
      }
    });
  } catch (error) {
    console.error('Error fetching case time entries:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;