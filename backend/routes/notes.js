const express = require('express');
const router = express.Router();
const Note = require('../models/Note');
const { auth } = require('../middleware/auth');
const { cleanObjectIds, validateRequiredFields } = require('../middleware/validation');

// GET /api/notes - Get all notes with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const search = req.query.search;

    const query = {};
    if (caseId) query.caseId = caseId;
    
    if (search) {
      query.$or = [
        { subject: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } }
      ];
    }

    const notes = await Note.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('createdBy', 'firstName lastName')
      .sort({ noteDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Note.countDocuments(query);

    res.json({
      notes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching notes:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/notes - Create new note
router.post('/', auth, cleanObjectIds, validateRequiredFields(['subject', 'content', 'caseId']), async (req, res) => {
  try {
    const noteData = {
      ...req.body,
      createdBy: req.user.id
    };

    const note = new Note(noteData);
    await note.save();

    await note.populate('caseId', 'caseNumber caseName clientName');
    await note.populate('createdBy', 'firstName lastName');

    res.status(201).json(note);
  } catch (error) {
    console.error('Error creating note:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/notes/:id - Update note
router.put('/:id', auth, cleanObjectIds, async (req, res) => {
  try {
    const note = await Note.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('createdBy', 'firstName lastName');

    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    res.json(note);
  } catch (error) {
    console.error('Error updating note:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/notes/:id - Delete note
router.delete('/:id', auth, async (req, res) => {
  try {
    const note = await Note.findByIdAndDelete(req.params.id);

    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    res.json({ message: 'Note deleted successfully' });
  } catch (error) {
    console.error('Error deleting note:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;