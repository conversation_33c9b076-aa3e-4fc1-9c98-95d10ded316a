const express = require('express');
const router = express.Router();
const Invoice = require('../models/Invoice');
const Case = require('../models/Case');
const TimeEntry = require('../models/TimeEntry');
const Expense = require('../models/Expense');
const { auth } = require('../middleware/auth');

// GET /api/invoices - Get all invoices with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const status = req.query.status;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    // Build query
    const query = {};
    
    if (caseId) query.caseId = caseId;
    if (status) query.status = status;
    
    if (startDate || endDate) {
      query.invoiceDate = {};
      if (startDate) query.invoiceDate.$gte = new Date(startDate);
      if (endDate) query.invoiceDate.$lte = new Date(endDate);
    }

    const invoices = await Invoice.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('billingUser', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .sort({ invoiceDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Invoice.countDocuments(query);

    // Calculate totals
    const totals = await Invoice.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          totalOutstanding: { $sum: '$balanceDue' }
        }
      }
    ]);

    const summary = totals[0] || {
      totalAmount: 0,
      totalPaid: 0,
      totalOutstanding: 0
    };

    res.json({
      invoices,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary
    });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/invoices/:id - Get single invoice
router.get('/:id', auth, async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('billingUser', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .populate('timeEntries')
      .populate('expenses')
      .populate('flatFees');

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.json(invoice);
  } catch (error) {
    console.error('Error fetching invoice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/invoices - Create new invoice
router.post('/', auth, async (req, res) => {
  try {
    const invoiceData = {
      ...req.body,
      createdBy: req.user.id,
      billingUser: req.body.billingUser || req.user.id
    };

    // Calculate totals based on included items
    if (invoiceData.timeEntries && invoiceData.timeEntries.length > 0) {
      const timeEntries = await TimeEntry.find({ 
        _id: { $in: invoiceData.timeEntries } 
      });
      invoiceData.timeEntryTotal = timeEntries.reduce((sum, entry) => sum + entry.amount, 0);
    }

    if (invoiceData.expenses && invoiceData.expenses.length > 0) {
      const expenses = await Expense.find({ 
        _id: { $in: invoiceData.expenses } 
      });
      invoiceData.expenseTotal = expenses.reduce((sum, expense) => sum + expense.total, 0);
    }

    const invoice = new Invoice(invoiceData);
    await invoice.save();

    // Populate the response
    await invoice.populate('caseId', 'caseNumber caseName clientName');
    await invoice.populate('billingUser', 'firstName lastName');
    await invoice.populate('createdBy', 'firstName lastName');

    res.status(201).json(invoice);
  } catch (error) {
    console.error('Error creating invoice:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/invoices/:id - Update invoice
router.put('/:id', auth, async (req, res) => {
  try {
    const updateData = { ...req.body };
    
    // Recalculate totals if items changed
    if (updateData.timeEntries || updateData.expenses) {
      if (updateData.timeEntries && updateData.timeEntries.length > 0) {
        const timeEntries = await TimeEntry.find({ 
          _id: { $in: updateData.timeEntries } 
        });
        updateData.timeEntryTotal = timeEntries.reduce((sum, entry) => sum + entry.amount, 0);
      }

      if (updateData.expenses && updateData.expenses.length > 0) {
        const expenses = await Expense.find({ 
          _id: { $in: updateData.expenses } 
        });
        updateData.expenseTotal = expenses.reduce((sum, expense) => sum + expense.total, 0);
      }
    }

    const invoice = await Invoice.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('billingUser', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.json(invoice);
  } catch (error) {
    console.error('Error updating invoice:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/invoices/:id - Delete invoice
router.delete('/:id', auth, async (req, res) => {
  try {
    const invoice = await Invoice.findByIdAndDelete(req.params.id);

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.json({ message: 'Invoice deleted successfully' });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/invoices/:id/status - Update invoice status
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { status } = req.body;
    
    const updateData = { status };
    
    // Set status-specific dates
    if (status === 'sent' && !req.body.sentDate) {
      updateData.sent = true;
      updateData.sentDate = new Date();
    }
    
    if (status === 'paid' && !req.body.paidDate) {
      updateData.paidDate = new Date();
      updateData.paidAmount = req.body.paidAmount || updateData.totalAmount;
    }

    const invoice = await Invoice.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('billingUser', 'firstName lastName');

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    res.json(invoice);
  } catch (error) {
    console.error('Error updating invoice status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/invoices/:id/pdf - Generate invoice PDF
router.get('/:id/pdf', auth, async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate('caseId')
      .populate('billingUser')
      .populate('timeEntries')
      .populate('expenses');

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Generate PDF
    const PDFGenerator = require('../utils/pdfGenerator');
    const pdfBuffer = await PDFGenerator.generateInvoicePDF(invoice);

    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="invoice-${invoice.invoiceNumber}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating invoice PDF:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/invoices/reports/summary - Get invoice summary report
router.get('/reports/summary', auth, async (req, res) => {
  try {
    const { startDate, endDate, caseId, status } = req.query;
    
    const matchStage = {};
    if (startDate || endDate) {
      matchStage.invoiceDate = {};
      if (startDate) matchStage.invoiceDate.$gte = new Date(startDate);
      if (endDate) matchStage.invoiceDate.$lte = new Date(endDate);
    }
    if (caseId) matchStage.caseId = mongoose.Types.ObjectId(caseId);
    if (status) matchStage.status = status;

    const summary = await Invoice.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalInvoices: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          totalOutstanding: { $sum: '$balanceDue' },
          avgInvoiceAmount: { $avg: '$totalAmount' }
        }
      }
    ]);

    // Status breakdown
    const statusBreakdown = await Invoice.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' }
        }
      }
    ]);

    const result = summary[0] || {
      totalInvoices: 0,
      totalAmount: 0,
      totalPaid: 0,
      totalOutstanding: 0,
      avgInvoiceAmount: 0
    };

    result.statusBreakdown = statusBreakdown;

    res.json(result);
  } catch (error) {
    console.error('Error generating invoice summary:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;