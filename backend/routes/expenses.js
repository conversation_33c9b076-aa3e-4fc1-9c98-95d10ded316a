const express = require('express');
const router = express.Router();
const Expense = require('../models/Expense');
const Case = require('../models/Case');
const { auth } = require('../middleware/auth');
const { cleanObjectIds, validateRequiredFields } = require('../middleware/validation');

// GET /api/expenses - Get all expenses with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const userId = req.query.userId;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    const billable = req.query.billable;
    const category = req.query.category;

    // Build query
    const query = {};
    
    if (caseId) query.caseId = caseId;
    if (userId) query.user = userId;
    if (billable !== undefined) query.nonbillable = billable === 'false';
    if (category) query.category = category;
    
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    const expenses = await Expense.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName')
      .populate('invoiceId', 'invoiceNumber')
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Expense.countDocuments(query);

    // Calculate totals
    const totalSummary = await Expense.aggregate([
      { $match: query },
      { $group: { _id: null, totalQuantity: { $sum: '$quantity' }, totalCost: { $sum: '$total' } } }
    ]);

    res.json({
      expenses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        totalQuantity: totalSummary[0]?.totalQuantity || 0,
        totalCost: totalSummary[0]?.totalCost || 0
      }
    });
  } catch (error) {
    console.error('Error fetching expenses:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/expenses/:id - Get single expense
router.get('/:id', auth, async (req, res) => {
  try {
    const expense = await Expense.findById(req.params.id)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName')
      .populate('invoiceId', 'invoiceNumber');

    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }

    res.json(expense);
  } catch (error) {
    console.error('Error fetching expense:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/expenses - Create new expense
router.post('/', auth, cleanObjectIds, validateRequiredFields(['date', 'activity', 'quantity', 'cost', 'description', 'caseId']), async (req, res) => {
  try {
    const expenseData = {
      ...req.body,
      user: req.body.user || req.user.id
    };

    // Calculate total if not provided
    if (!expenseData.total && expenseData.quantity && expenseData.cost) {
      expenseData.total = expenseData.quantity * expenseData.cost;
    }

    const expense = new Expense(expenseData);
    await expense.save();

    // Populate the response
    await expense.populate('caseId', 'caseNumber caseName clientName');
    await expense.populate('user', 'firstName lastName');
    await expense.populate('invoiceId', 'invoiceNumber');

    res.status(201).json(expense);
  } catch (error) {
    console.error('Error creating expense:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/expenses/:id - Update expense
router.put('/:id', auth, cleanObjectIds, async (req, res) => {
  try {
    const updateData = { ...req.body };
    
    // Recalculate total if quantity or cost changed
    if ((updateData.quantity || updateData.cost) && updateData.quantity && updateData.cost) {
      updateData.total = updateData.quantity * updateData.cost;
    }

    const expense = await Expense.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('user', 'firstName lastName')
    .populate('invoiceId', 'invoiceNumber');

    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }

    res.json(expense);
  } catch (error) {
    console.error('Error updating expense:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/expenses/:id - Delete expense
router.delete('/:id', auth, async (req, res) => {
  try {
    const expense = await Expense.findByIdAndDelete(req.params.id);

    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }

    res.json({ message: 'Expense deleted successfully' });
  } catch (error) {
    console.error('Error deleting expense:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/expenses/bulk - Create multiple expenses
router.post('/bulk', auth, async (req, res) => {
  try {
    const { expenses } = req.body;
    
    if (!Array.isArray(expenses)) {
      return res.status(400).json({ message: 'Expenses must be an array' });
    }

    // Add user and calculate totals
    const processedExpenses = expenses.map(expense => {
      const processedExpense = {
        ...expense,
        user: expense.user || req.user.id
      };
      
      if (!processedExpense.total && processedExpense.quantity && processedExpense.cost) {
        processedExpense.total = processedExpense.quantity * processedExpense.cost;
      }
      
      return processedExpense;
    });

    const createdExpenses = await Expense.insertMany(processedExpenses);
    
    // Populate the created expenses
    const populatedExpenses = await Expense.find({
      _id: { $in: createdExpenses.map(e => e._id) }
    })
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('user', 'firstName lastName')
    .populate('invoiceId', 'invoiceNumber');

    res.status(201).json({ 
      message: `${createdExpenses.length} expenses created successfully`,
      expenses: populatedExpenses 
    });
  } catch (error) {
    console.error('Error creating bulk expenses:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/expenses/reports/summary - Get expense summary report
router.get('/reports/summary', auth, async (req, res) => {
  try {
    const { startDate, endDate, caseId, userId, category } = req.query;
    
    const matchStage = {};
    if (startDate || endDate) {
      matchStage.date = {};
      if (startDate) matchStage.date.$gte = new Date(startDate);
      if (endDate) matchStage.date.$lte = new Date(endDate);
    }
    if (caseId) matchStage.caseId = mongoose.Types.ObjectId(caseId);
    if (userId) matchStage.user = mongoose.Types.ObjectId(userId);
    if (category) matchStage.category = category;

    const summary = await Expense.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalQuantity: { $sum: '$quantity' },
          totalCost: { $sum: '$total' },
          billableTotal: { $sum: { $cond: [{ $eq: ['$nonbillable', false] }, '$total', 0] } },
          nonbillableTotal: { $sum: { $cond: ['$nonbillable', '$total', 0] } },
          billedTotal: { $sum: { $cond: ['$billed', '$total', 0] } },
          unbilledTotal: { $sum: { $cond: [{ $eq: ['$billed', false] }, '$total', 0] } },
          expenseCount: { $sum: 1 }
        }
      }
    ]);

    // Category breakdown
    const categoryBreakdown = await Expense.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          total: { $sum: '$total' }
        }
      },
      { $sort: { total: -1 } }
    ]);

    const result = summary[0] || {
      totalQuantity: 0,
      totalCost: 0,
      billableTotal: 0,
      nonbillableTotal: 0,
      billedTotal: 0,
      unbilledTotal: 0,
      expenseCount: 0
    };

    result.categoryBreakdown = categoryBreakdown;

    res.json(result);
  } catch (error) {
    console.error('Error generating expense summary:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;