const express = require('express');
const router = express.Router();
const FlatFee = require('../models/FlatFee');
const { auth } = require('../middleware/auth');

// GET /api/flat-fees - Get all flat fees with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const billed = req.query.billed;
    const search = req.query.search;

    const query = {};
    if (caseId) query.caseId = caseId;
    if (billed !== undefined) query.billed = billed === 'true';
    
    if (search) {
      query.$or = [
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const flatFees = await FlatFee.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName')
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit);

    const total = await FlatFee.countDocuments(query);

    res.json({
      flatFees,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching flat fees:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/flat-fees - Create new flat fee
router.post('/', auth, async (req, res) => {
  try {
    const flatFeeData = {
      ...req.body,
      user: req.user.id
    };

    const flatFee = new FlatFee(flatFeeData);
    await flatFee.save();

    await flatFee.populate('caseId', 'caseNumber caseName clientName');
    await flatFee.populate('user', 'firstName lastName');

    res.status(201).json(flatFee);
  } catch (error) {
    console.error('Error creating flat fee:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/flat-fees/:id - Update flat fee
router.put('/:id', auth, async (req, res) => {
  try {
    const flatFee = await FlatFee.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('user', 'firstName lastName');

    if (!flatFee) {
      return res.status(404).json({ message: 'Flat fee not found' });
    }

    res.json(flatFee);
  } catch (error) {
    console.error('Error updating flat fee:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/flat-fees/:id - Delete flat fee
router.delete('/:id', auth, async (req, res) => {
  try {
    const flatFee = await FlatFee.findByIdAndDelete(req.params.id);

    if (!flatFee) {
      return res.status(404).json({ message: 'Flat fee not found' });
    }

    res.json({ message: 'Flat fee deleted successfully' });
  } catch (error) {
    console.error('Error deleting flat fee:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;