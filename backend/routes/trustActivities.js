const express = require('express');
const router = express.Router();
const TrustActivity = require('../models/TrustActivity');
const { auth } = require('../middleware/auth');

// GET /api/trust-activities - Get all trust activities with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const type = req.query.type;
    const search = req.query.search;

    const query = {};
    if (caseId) query.caseId = caseId;
    if (type) query.type = type;
    
    if (search) {
      query.$or = [
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const trustActivities = await TrustActivity.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('user', 'firstName lastName')
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TrustActivity.countDocuments(query);

    res.json({
      trustActivities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching trust activities:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/trust-activities - Create new trust activity
router.post('/', auth, async (req, res) => {
  try {
    const trustActivityData = {
      ...req.body,
      user: req.user.id
    };

    const trustActivity = new TrustActivity(trustActivityData);
    await trustActivity.save();

    await trustActivity.populate('caseId', 'caseNumber caseName clientName');
    await trustActivity.populate('user', 'firstName lastName');

    res.status(201).json(trustActivity);
  } catch (error) {
    console.error('Error creating trust activity:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/trust-activities/:id - Update trust activity
router.put('/:id', auth, async (req, res) => {
  try {
    const trustActivity = await TrustActivity.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('user', 'firstName lastName');

    if (!trustActivity) {
      return res.status(404).json({ message: 'Trust activity not found' });
    }

    res.json(trustActivity);
  } catch (error) {
    console.error('Error updating trust activity:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/trust-activities/:id - Delete trust activity
router.delete('/:id', auth, async (req, res) => {
  try {
    const trustActivity = await TrustActivity.findByIdAndDelete(req.params.id);

    if (!trustActivity) {
      return res.status(404).json({ message: 'Trust activity not found' });
    }

    res.json({ message: 'Trust activity deleted successfully' });
  } catch (error) {
    console.error('Error deleting trust activity:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;