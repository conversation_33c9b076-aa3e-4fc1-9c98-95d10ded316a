const express = require('express');
const multer = require('multer');
const path = require('path');
const { body, validationResult } = require('express-validator');
const Case = require('../models/Case');
const { auth } = require('../middleware/auth');
const { cleanObjectIds, validateRequiredFields } = require('../middleware/validation');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Get all cases
router.get('/', auth, async (req, res) => {
  try {
    const { status, caseType, page = 1, limit = 10, search } = req.query;
    const query = {};
    
    if (status) query.status = status;
    if (caseType) query.caseType = caseType;
    
    // Comprehensive search across multiple fields
    if (search) {
      query.$or = [
        { caseName: { $regex: search, $options: 'i' } },
        { clientName: { $regex: search, $options: 'i' } },
        { caseNumber: { $regex: search, $options: 'i' } },
        { clientPhone: { $regex: search, $options: 'i' } },
        { clientEmail: { $regex: search, $options: 'i' } },
        { alienNumber: { $regex: search, $options: 'i' } },
        { contacts: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const cases = await Case.find(query)
      .populate('attorney', 'firstName lastName')
      .populate('caseManager', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Case.countDocuments(query);

    res.json({
      cases,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new case
router.post('/', [
  auth,
  cleanObjectIds,
  body('clientName').trim().notEmpty().withMessage('Client name is required'),
  body('clientEmail').optional().isEmail().normalizeEmail().withMessage('Invalid email format'),
  body('clientPhone').optional().trim(),
  body('caseType').isIn(['personal_injury', 'criminal_defense', 'family_law', 'family_law_tx', 'family_law_il', 'civil_litigation', 'business_law', 'estate_planning', 'immigration', 'employment_law', 'real_estate', 'other']).withMessage('Invalid case type'),
  body('description').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const caseData = new Case({
      ...req.body,
      attorney: req.user._id
    });

    await caseData.save();
    await caseData.populate('attorney', 'firstName lastName');
    
    res.status(201).json(caseData);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get single case
router.get('/:id', auth, async (req, res) => {
  try {
    const caseData = await Case.findById(req.params.id)
      .populate('attorney', 'firstName lastName')
      .populate('caseManager', 'firstName lastName')
      .populate('documents.uploadedBy', 'firstName lastName')
      .populate('notes.createdBy', 'firstName lastName');
    
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    res.json(caseData);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update case
router.put('/:id', auth, cleanObjectIds, async (req, res) => {
  try {
    const caseData = await Case.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('attorney', 'firstName lastName')
     .populate('caseManager', 'firstName lastName');
    
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    res.json(caseData);
  } catch (error) {
    console.error(error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: 'Validation error', errors: error.errors });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete case
router.delete('/:id', auth, async (req, res) => {
  try {
    const caseData = await Case.findById(req.params.id);
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    // Check if user has permission to delete this case
    if (caseData.attorney && caseData.attorney.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this case' });
    }

    await Case.findByIdAndDelete(req.params.id);
    res.json({ message: 'Case deleted successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Upload document
router.post('/:id/documents', auth, upload.single('document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const caseData = await Case.findById(req.params.id);
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    const document = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      mimetype: req.file.mimetype,
      size: req.file.size,
      uploadedBy: req.user._id,
      category: req.body.category || 'other'
    };

    caseData.documents.push(document);
    await caseData.save();
    await caseData.populate('documents.uploadedBy', 'firstName lastName');
    
    res.json(caseData);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Add important date
router.post('/:id/dates', [
  auth,
  body('title').trim().notEmpty(),
  body('date').isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const caseData = await Case.findById(req.params.id);
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    // Add the main date
    caseData.importantDates.push(req.body);
    
    // If this is a court date or merit hearing, automatically create associated deadlines
    if (req.body.type === 'court_date') {
      const courtDate = new Date(req.body.date);
      const deadlines = generateCourtDeadlines(courtDate, caseData.caseType, req.body.title);
      
      // Add each generated deadline
      deadlines.forEach(deadline => {
        caseData.importantDates.push(deadline);
      });
      
      console.log(`Added court date and ${deadlines.length} automatic deadlines for case ${caseData.caseNumber}`);
    } else if (req.body.type === 'merit_hearing') {
      const individualHearingDate = new Date(req.body.date);
      const deadlines = generateIndividualHearingDeadlines(individualHearingDate, req.body.title);
      
      // Add each generated deadline
      deadlines.forEach(deadline => {
        caseData.importantDates.push(deadline);
      });
      
      console.log(`Added Individual Hearing and ${deadlines.length} automatic Immigration deadlines for case ${caseData.caseNumber}`);
    }
    
    await caseData.save();
    
    res.json(caseData);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to generate automatic deadlines based on court date
function generateCourtDeadlines(courtDate, caseType, courtTitle) {
  const deadlines = [];
  
  // Common deadlines for all case types
  const commonDeadlines = [
    {
      days: 7,
      title: 'Prepare Court Documents',
      description: `Finalize all documents needed for ${courtTitle}`
    },
    {
      days: 3,
      title: 'Review Case Strategy',
      description: `Final review of strategy and talking points for ${courtTitle}`
    },
    {
      days: 1,
      title: 'Client Preparation Call',
      description: `Prepare client for what to expect during ${courtTitle}`
    }
  ];
  
  // Case-type specific deadlines
  const specificDeadlines = {
    criminal_defense: [
      {
        days: 14,
        title: 'Evidence Review Deadline',
        description: 'Complete review of prosecution evidence'
      },
      {
        days: 10,
        title: 'Witness Interview Completion',
        description: 'Finish all defense witness interviews'
      }
    ],
    personal_injury: [
      {
        days: 21,
        title: 'Medical Records Collection',
        description: 'Gather all relevant medical documentation'
      },
      {
        days: 14,
        title: 'Expert Witness Preparation',
        description: 'Prepare medical expert testimonies'
      }
    ],
    family_law: [
      {
        days: 14,
        title: 'Financial Documentation',
        description: 'Compile all financial records and statements'
      },
      {
        days: 7,
        title: 'Child Custody Evaluation Review',
        description: 'Review custody evaluation reports if applicable'
      }
    ],
    business_law: [
      {
        days: 21,
        title: 'Contract Analysis Completion',
        description: 'Complete analysis of all relevant contracts'
      },
      {
        days: 14,
        title: 'Financial Impact Assessment',
        description: 'Assess financial implications and damages'
      }
    ],
    estate_planning: [
      {
        days: 14,
        title: 'Asset Documentation Review',
        description: 'Review all asset documentation and valuations'
      }
    ],
    immigration: [
      {
        days: 21,
        title: 'Immigration Document Review',
        description: 'Review all immigration paperwork and supporting documents'
      },
      {
        days: 14,
        title: 'Translation Services',
        description: 'Ensure all documents are properly translated'
      }
    ]
  };
  
  // Combine common and specific deadlines
  const allDeadlines = [
    ...commonDeadlines,
    ...(specificDeadlines[caseType] || [])
  ];
  
  // Generate deadline dates
  allDeadlines.forEach(deadline => {
    const deadlineDate = new Date(courtDate);
    deadlineDate.setDate(deadlineDate.getDate() - deadline.days);
    
    // Only add deadlines that are in the future
    if (deadlineDate > new Date()) {
      deadlines.push({
        title: deadline.title,
        date: deadlineDate.toISOString(),
        description: deadline.description,
        type: 'deadline'
      });
    }
  });
  
  return deadlines;
}

// Helper function to generate Immigration Individual Hearing specific deadlines
function generateIndividualHearingDeadlines(individualHearingDate, courtTitle) {
  const deadlines = [];
  
  // Calculate key dates based on Individual Hearing
  const callUpDate = new Date(individualHearingDate);
  callUpDate.setDate(callUpDate.getDate() - 30); // 30 days before IH (standard, unless scheduling order states otherwise)
  
  const evidenceCutoffDate = new Date(callUpDate);
  evidenceCutoffDate.setDate(evidenceCutoffDate.getDate() - 30); // One month before call-up date
  
  const ihEvidenceBrainstormDate = new Date(); // Current date + 1-2 weeks (we'll use 10 days as middle ground)
  ihEvidenceBrainstormDate.setDate(ihEvidenceBrainstormDate.getDate() + 10);
  
  const paymentDueAndFinalEvidenceDate = new Date(callUpDate);
  paymentDueAndFinalEvidenceDate.setDate(paymentDueAndFinalEvidenceDate.getDate() - 30); // 30 days before call-up
  
  const finalPrepDate = new Date(individualHearingDate);
  finalPrepDate.setDate(finalPrepDate.getDate() - 14); // 2 weeks before Individual Hearing
  
  const motionDeadlineDate = new Date(individualHearingDate);
  motionDeadlineDate.setDate(motionDeadlineDate.getDate() - 60); // 60 days before Individual Hearing
  
  const today = new Date();
  
  // Only add deadlines that are in the future
  
  // 1. Call-Up Date (calendar event, not task)
  if (callUpDate > today) {
    deadlines.push({
      title: 'Call-Up Date',
      date: callUpDate.toISOString(),
      description: 'Immigration Call-Up Date (standard 30 days before Individual Hearing, unless scheduling order states otherwise)',
      type: 'call_up_date'
    });
  }
  
  // 2. IH Evidence Brainstorm Meeting (1-2 weeks after last master hearing, using current date + 10 days)
  if (ihEvidenceBrainstormDate > today) {
    deadlines.push({
      title: 'IH Evidence Brainstorm Meeting',
      date: ihEvidenceBrainstormDate.toISOString(),
      description: 'Meeting with Client + Case Manager + Attorney: Attorney review I-589 and make amendments, brainstorm supporting evidence list, share evidence checklist with client. Evidence due ASAP but CUT-OFF is one month before call-up date.',
      type: 'meeting'
    });
  }
  
  // 3. Payment Due Date + IH Final Collection of Evidence (30 days before Call-up Date)
  if (paymentDueAndFinalEvidenceDate > today) {
    deadlines.push({
      title: 'Payment Due Date + IH Final Evidence Collection',
      date: paymentDueAndFinalEvidenceDate.toISOString(),
      description: 'DEADLINE: Client + Case Manager meeting. Client must finish payment by this date AND bring all required evidence. This allows attorney to work on brief and final hearing prep.',
      type: 'deadline'
    });
  }
  
  // 4. IH Final Prep (2 weeks before Individual Hearing)
  if (finalPrepDate > today) {
    deadlines.push({
      title: 'IH Final Prep Meeting',
      date: finalPrepDate.toISOString(),
      description: '1-hour meeting with Client + Case Manager + Attorney. Client must be familiar with story and written declaration. Attorney will conduct direct and cross examination practice.',
      type: 'meeting'
    });
  }
  
  // 5. Motion to Change Venue/Motion for Continuance (60 days before Individual Hearing)
  if (motionDeadlineDate > today) {
    deadlines.push({
      title: 'Motion to Change Venue/Continuance Deadline',
      date: motionDeadlineDate.toISOString(),
      description: 'DEADLINE to file Motion to Change Venue (if approval chances are low in TX and want to move to different state) or Motion for Continuance (60 days before Individual Hearing unless judge states otherwise)',
      type: 'deadline'
    });
  }
  
  // 6. Motion to Withdraw Deadline (60 days before Individual Hearing)
  if (motionDeadlineDate > today) {
    deadlines.push({
      title: 'Motion to Withdraw Deadline',
      date: motionDeadlineDate.toISOString(),
      description: 'DEADLINE to file Motion to Withdraw (if client stops payment or loses contact) - 60 days before Individual Hearing unless judge states otherwise',
      type: 'deadline'
    });
  }
  
  // 7. Evidence Cut-off Reminder (separate from Payment Due for clarity)
  if (evidenceCutoffDate > today) {
    deadlines.push({
      title: 'Evidence Cut-off Date',
      date: evidenceCutoffDate.toISOString(),
      description: 'FINAL CUT-OFF for all supporting evidence. No evidence accepted after this date (one month before call-up date).',
      type: 'deadline'
    });
  }
  
  return deadlines;
}

// Add note to case
router.post('/:id/notes', [
  auth,
  body('content').trim().notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const caseData = await Case.findById(req.params.id);
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    caseData.notes.push({
      content: req.body.content,
      createdBy: req.user._id
    });

    await caseData.save();
    await caseData.populate('notes.createdBy', 'firstName lastName');
    
    res.json(caseData);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;