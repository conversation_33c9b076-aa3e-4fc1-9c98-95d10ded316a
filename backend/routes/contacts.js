const express = require('express');
const router = express.Router();
const Contact = require('../models/Contact');

// GET /api/contacts - Get all contacts with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';
    const type = req.query.type || '';
    const archived = req.query.archived === 'true';

    // Build query
    const query = { archived };
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { 'phones.mobile': { $regex: search, $options: 'i' } },
        { 'phones.home': { $regex: search, $options: 'i' } },
        { 'phones.work': { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      query.contactGroup = type;
    }

    const contacts = await Contact.find(query)
      .sort({ lastName: 1, firstName: 1 })
      .skip(skip)
      .limit(limit)
      .populate('cases', 'caseNumber caseName status');

    const total = await Contact.countDocuments(query);

    res.json({
      contacts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/contacts/:id - Get single contact
router.get('/:id', async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id)
      .populate('cases', 'caseNumber caseName status practiceArea openDate');

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    res.json(contact);
  } catch (error) {
    console.error('Error fetching contact:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/contacts - Create new contact
router.post('/', async (req, res) => {
  try {
    const contact = new Contact(req.body);
    await contact.save();
    
    res.status(201).json(contact);
  } catch (error) {
    console.error('Error creating contact:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/contacts/:id - Update contact
router.put('/:id', async (req, res) => {
  try {
    const contact = await Contact.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    res.json(contact);
  } catch (error) {
    console.error('Error updating contact:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/contacts/:id - Delete contact (soft delete by archiving)
router.delete('/:id', async (req, res) => {
  try {
    const contact = await Contact.findByIdAndUpdate(
      req.params.id,
      { archived: true },
      { new: true }
    );

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    res.json({ message: 'Contact archived successfully' });
  } catch (error) {
    console.error('Error archiving contact:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/contacts/:id/restore - Restore archived contact
router.post('/:id/restore', async (req, res) => {
  try {
    const contact = await Contact.findByIdAndUpdate(
      req.params.id,
      { archived: false },
      { new: true }
    );

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    res.json(contact);
  } catch (error) {
    console.error('Error restoring contact:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/contacts/search/quick - Quick search for contact picker
router.get('/search/quick', async (req, res) => {
  try {
    const search = req.query.q || '';
    const limit = parseInt(req.query.limit) || 10;

    if (!search) {
      return res.json([]);
    }

    const contacts = await Contact.find({
      archived: false,
      $or: [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ]
    })
    .limit(limit)
    .select('firstName lastName company email phones._id')
    .sort({ lastName: 1, firstName: 1 });

    res.json(contacts);
  } catch (error) {
    console.error('Error in quick search:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;