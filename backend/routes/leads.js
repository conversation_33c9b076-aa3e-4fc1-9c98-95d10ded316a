const express = require('express');
const router = express.Router();
const Lead = require('../models/Lead');
const User = require('../models/User');
const { auth } = require('../middleware/auth');

// GET /api/leads - Get all leads with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const caseType = req.query.caseType;
    const source = req.query.source;
    const assignedTo = req.query.assignedTo;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    const search = req.query.search;

    // Build query
    const query = {};
    
    if (status) query.status = status;
    if (caseType) query.caseType = caseType;
    if (source) query.source = source;
    if (assignedTo) query.assignedTo = assignedTo;
    
    if (startDate || endDate) {
      query.addedDate = {};
      if (startDate) query.addedDate.$gte = new Date(startDate);
      if (endDate) query.addedDate.$lte = new Date(endDate);
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { 'contact.email': { $regex: search, $options: 'i' } },
        { details: { $regex: search, $options: 'i' } },
        { potentialCaseName: { $regex: search, $options: 'i' } }
      ];
    }

    const leads = await Lead.find(query)
      .populate('assignedTo', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .sort({ addedDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Lead.countDocuments(query);

    // Calculate summary stats
    const statusSummary = await Lead.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalValue: { $sum: '$value' }
        }
      }
    ]);

    const totalValue = await Lead.aggregate([
      { $match: query },
      { $group: { _id: null, totalValue: { $sum: '$value' } } }
    ]);

    res.json({
      leads,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        statusBreakdown: statusSummary,
        totalValue: totalValue[0]?.totalValue || 0
      }
    });
  } catch (error) {
    console.error('Error fetching leads:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/leads/staff - Get leads grouped by staff (MUST come before /:id)
router.get('/staff', auth, async (req, res) => {
  try {
    // Get all users who are staff
    const staff = await User.find({}, 'firstName lastName').sort({ firstName: 1 });
    
    // Get leads grouped by assigned staff with priority sorting
    // Handle both ObjectId and string assignedTo values
    const leadsGrouped = await Lead.aggregate([
      { $sort: { priority: -1, addedDate: -1 } },
      {
        $group: {
          _id: '$assignedTo',
          leads: { $push: '$$ROOT' },
          count: { $sum: 1 },
          totalValue: { $sum: '$value' },
          converted: { $sum: { $cond: [{ $eq: ['$status', 'converted'] }, 1, 0] } }
        }
      }
    ]);

    // Format data for each staff member
    const staffLeads = staff.map(staffMember => {
      const staffData = leadsGrouped.find(group => 
        group._id && group._id.toString() === staffMember._id.toString()
      );
      
      return {
        staffId: staffMember._id,
        staffName: `${staffMember.firstName} ${staffMember.lastName}`,
        leads: staffData?.leads || [],
        count: staffData?.count || 0,
        totalValue: staffData?.totalValue || 0,
        converted: staffData?.converted || 0,
        conversionRate: staffData ? ((staffData.converted / staffData.count) * 100).toFixed(1) : 0
      };
    });

    // Also get unassigned leads (those without assignedTo or with non-ObjectId values)
    const unassignedLeads = await Lead.find({ 
      $or: [
        { assignedTo: { $exists: false } },
        { assignedTo: null },
        { assignedTo: { $type: "string" } }  // Include string values
      ]
    });
    
    staffLeads.push({
      staffId: null,
      staffName: 'Unassigned',
      leads: unassignedLeads,
      count: unassignedLeads.length,
      totalValue: unassignedLeads.reduce((sum, lead) => sum + (lead.value || 0), 0),
      converted: unassignedLeads.filter(lead => lead.status === 'converted').length,
      conversionRate: unassignedLeads.length > 0 ? 
        ((unassignedLeads.filter(lead => lead.status === 'converted').length / unassignedLeads.length) * 100).toFixed(1) : 0
    });

    res.json({ staffLeads });
  } catch (error) {
    console.error('Error fetching staff leads:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/leads/reports/summary - Get leads summary report (MUST come before /:id)
router.get('/reports/summary', auth, async (req, res) => {
  try {
    const { startDate, endDate, status, caseType, source } = req.query;
    
    const matchStage = {};
    if (startDate || endDate) {
      matchStage.addedDate = {};
      if (startDate) matchStage.addedDate.$gte = new Date(startDate);
      if (endDate) matchStage.addedDate.$lte = new Date(endDate);
    }
    if (status) matchStage.status = status;
    if (caseType) matchStage.caseType = caseType;
    if (source) matchStage.source = source;

    const summary = await Lead.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalLeads: { $sum: 1 },
          totalValue: { $sum: '$value' },
          converted: { $sum: { $cond: [{ $eq: ['$status', 'converted'] }, 1, 0] } },
          avgValue: { $avg: '$value' }
        }
      }
    ]);

    // Status breakdown
    const statusBreakdown = await Lead.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalValue: { $sum: '$value' }
        }
      }
    ]);

    // Source breakdown
    const sourceBreakdown = await Lead.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$source',
          count: { $sum: 1 },
          totalValue: { $sum: '$value' }
        }
      }
    ]);

    const result = summary[0] || {
      totalLeads: 0,
      totalValue: 0,
      converted: 0,
      avgValue: 0
    };

    result.statusBreakdown = statusBreakdown;
    result.sourceBreakdown = sourceBreakdown;
    result.conversionRate = result.totalLeads > 0 ? (result.converted / result.totalLeads * 100).toFixed(1) : 0;

    res.json(result);
  } catch (error) {
    console.error('Error generating leads summary:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/leads/:id - Get single lead (MUST come after specific routes)
router.get('/:id', auth, async (req, res) => {
  try {
    const lead = await Lead.findById(req.params.id)
      .populate('assignedTo', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName');

    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    res.json(lead);
  } catch (error) {
    console.error('Error fetching lead:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/leads - Create new lead
router.post('/', auth, async (req, res) => {
  try {
    const leadData = {
      ...req.body,
      createdBy: req.user.id,
      assignedTo: req.body.assignedTo || req.user.id
    };

    const lead = new Lead(leadData);
    await lead.save();

    // Populate the response
    await lead.populate('assignedTo', 'firstName lastName');
    await lead.populate('createdBy', 'firstName lastName');

    res.status(201).json(lead);
  } catch (error) {
    console.error('Error creating lead:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/leads/:id - Update lead
router.put('/:id', auth, async (req, res) => {
  try {
    const lead = await Lead.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
    .populate('assignedTo', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');

    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    res.json(lead);
  } catch (error) {
    console.error('Error updating lead:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/leads/:id - Delete lead
router.delete('/:id', auth, async (req, res) => {
  try {
    const lead = await Lead.findByIdAndDelete(req.params.id);

    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    res.json({ message: 'Lead deleted successfully' });
  } catch (error) {
    console.error('Error deleting lead:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/leads/:id/status - Update lead status
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { status } = req.body;
    
    const updateData = { status };
    
    // Set converted date if converting to converted status
    if (status === 'converted' && !req.body.convertedDate) {
      updateData.convertedDate = new Date();
    }

    const lead = await Lead.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('assignedTo', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');

    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    res.json(lead);
  } catch (error) {
    console.error('Error updating lead status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/leads/:id/priority - Update lead priority for drag and drop
router.put('/:id/priority', auth, async (req, res) => {
  try {
    const { priority } = req.body;
    
    const lead = await Lead.findByIdAndUpdate(
      req.params.id,
      { priority: priority || 0 },
      { new: true, runValidators: true }
    )
    .populate('assignedTo', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');

    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    res.json(lead);
  } catch (error) {
    console.error('Error updating lead priority:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;