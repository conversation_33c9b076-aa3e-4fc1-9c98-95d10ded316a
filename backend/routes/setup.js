const express = require('express');
const User = require('../models/User');
const Case = require('../models/Case');
const { adminAuth } = require('../middleware/auth');

const router = express.Router();

// Create default admin user
router.post('/create-admin', async (req, res) => {
  try {
    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      return res.json({ message: 'Admin user already exists' });
    }

    // Create admin user
    const admin = new User({
      email: '<EMAIL>',
      password: 'admin123',
      firstName: 'System',
      lastName: 'Administrator',
      role: 'admin'
    });

    await admin.save();
    
    res.json({ 
      message: 'Admin user created successfully',
      email: '<EMAIL>',
      password: 'admin123'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to create admin user' });
  }
});

// Test endpoint
router.post('/test', async (req, res) => {
  res.json({ message: 'Test endpoint works!' });
});

// Complete database reset and setup with sample data
router.post('/reset-database', adminAuth, async (req, res) => {
  try {
    console.log('=== RESETTING DATABASE ===');
    const { count = 50 } = req.body;
    
    // Clear all existing data except admin user
    await Case.deleteMany({});
    await User.deleteMany({ role: { $ne: 'admin' } });
    console.log('Cleared existing data');
    
    // Create sample attorneys
    const attorneys = [
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Attorney',
        role: 'attorney'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Sarah',
        lastName: 'Lawyer',
        role: 'attorney'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Mike',
        lastName: 'Counsel',
        role: 'attorney'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Emily',
        lastName: 'Defender',
        role: 'attorney'
      }
    ];
    
    // Create sample case managers
    const caseManagers = [
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Lisa',
        lastName: 'Manager',
        role: 'case_manager'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'David',
        lastName: 'Coordinator',
        role: 'case_manager'
      }
    ];
    
    // Create all users
    const allUsers = [...attorneys, ...caseManagers];
    const createdUsers = [];
    
    for (const userData of allUsers) {
      const user = new User(userData);
      await user.save();
      createdUsers.push(user);
    }
    
    const createdAttorneys = createdUsers.filter(u => u.role === 'attorney');
    const createdManagers = createdUsers.filter(u => u.role === 'case_manager');
    
    console.log(`Created ${createdAttorneys.length} attorneys and ${createdManagers.length} case managers`);
    
    // Now call the existing seed/cases endpoint logic to create cases
    const seedLogic = require('./seed');
    
    // We'll inline the case creation logic from seed.js here since we can't easily call the route
    const caseTypes = ['personal_injury', 'criminal_defense', 'family_law', 'business_law', 'estate_planning', 'immigration'];
    const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Jennifer', 'William', 'Amanda'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
    const streets = ['Main St', 'Oak Ave', 'Pine Rd', 'Maple Dr', 'Cedar Ln', 'Elm St', 'Park Ave'];
    const cities = ['Springfield', 'Franklin', 'Georgetown', 'Bristol', 'Clinton', 'Fairview'];
    const states = ['CA', 'TX', 'FL', 'NY', 'PA', 'IL'];
    
    const caseDescriptions = {
      personal_injury: ['Motor vehicle accident with injuries', 'Slip and fall at store', 'Workplace injury'],
      criminal_defense: ['DUI charges', 'Assault charges', 'Drug possession'],
      family_law: ['Divorce proceedings', 'Child custody', 'Adoption'],
      business_law: ['Contract dispute', 'Employment lawsuit', 'Business formation'],
      estate_planning: ['Will preparation', 'Trust establishment', 'Probate'],
      immigration: ['Citizenship application', 'Green card renewal', 'Visa application']
    };
    
    function getRandomElement(array) {
      return array[Math.floor(Math.random() * array.length)];
    }
    
    const cases = [];
    for (let i = 0; i < count; i++) {
      const caseType = getRandomElement(caseTypes);
      const firstName = getRandomElement(firstNames);
      const lastName = getRandomElement(lastNames);
      
      const caseData = {
        clientName: `${firstName} ${lastName}`,
        clientEmail: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
        clientPhone: `(${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
        clientAddress: {
          street: `${Math.floor(Math.random() * 9999) + 1} ${getRandomElement(streets)}`,
          city: getRandomElement(cities),
          state: getRandomElement(states),
          zipCode: `${Math.floor(Math.random() * 90000) + 10000}`
        },
        caseType: caseType,
        description: getRandomElement(caseDescriptions[caseType]),
        attorney: getRandomElement(createdAttorneys)._id,
        caseManager: Math.random() > 0.5 ? getRandomElement(createdManagers)._id : null,
        billingRate: (Math.floor(Math.random() * 200) + 150) * 5,
        totalBilled: Math.floor(Math.random() * 10000) + 1000,
        totalPaid: Math.floor(Math.random() * 8000) + 500,
        caseNumber: `CASE-${String(i + 1).padStart(6, '0')}`
      };
      
      // Generate some future important dates
      const today = new Date();
      const futureDate = new Date(today.getTime() + (Math.random() * 90 * 24 * 60 * 60 * 1000));
      const importantDates = [
        {
          title: `${caseType === 'criminal_defense' ? 'Court Hearing' : caseType === 'family_law' ? 'Mediation' : 'Client Meeting'}`,
          date: futureDate,
          description: `Scheduled ${caseType.replace('_', ' ')} related appointment`,
          type: Math.random() > 0.5 ? 'court_date' : 'meeting'
        }
      ];
      caseData.importantDates = importantDates;
      
      const newCase = new Case(caseData);
      await newCase.save();
      cases.push(newCase);
    }
    
    console.log(`Created ${cases.length} sample cases`);
    console.log('=== DATABASE RESET COMPLETE ===');
    
    res.json({
      message: 'Database reset and seeded successfully',
      data: {
        attorneys: createdAttorneys.length,
        caseManagers: createdManagers.length,
        cases: cases.length
      }
    });
    
  } catch (error) {
    console.error('Database reset error:', error);
    res.status(500).json({ message: 'Failed to reset database', error: error.message });
  }
});

module.exports = router;