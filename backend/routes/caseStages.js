const express = require('express');
const router = express.Router();
const CaseStage = require('../models/CaseStage');
const { auth } = require('../middleware/auth');

// GET /api/case-stages - Get all case stages with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const caseId = req.query.caseId;
    const status = req.query.status;
    const search = req.query.search;

    const query = {};
    if (caseId) query.caseId = caseId;
    if (status) query.status = status;
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const caseStages = await CaseStage.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .sort({ startDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await CaseStage.countDocuments(query);

    res.json({
      caseStages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching case stages:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/case-stages - Create new case stage
router.post('/', auth, async (req, res) => {
  try {
    const caseStage = new CaseStage(req.body);
    await caseStage.save();

    await caseStage.populate('caseId', 'caseNumber caseName clientName');

    res.status(201).json(caseStage);
  } catch (error) {
    console.error('Error creating case stage:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/case-stages/:id - Update case stage
router.put('/:id', auth, async (req, res) => {
  try {
    const caseStage = await CaseStage.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName');

    if (!caseStage) {
      return res.status(404).json({ message: 'Case stage not found' });
    }

    res.json(caseStage);
  } catch (error) {
    console.error('Error updating case stage:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/case-stages/:id - Delete case stage
router.delete('/:id', auth, async (req, res) => {
  try {
    const caseStage = await CaseStage.findByIdAndDelete(req.params.id);

    if (!caseStage) {
      return res.status(404).json({ message: 'Case stage not found' });
    }

    res.json({ message: 'Case stage deleted successfully' });
  } catch (error) {
    console.error('Error deleting case stage:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;