const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const Payment = require('../models/Payment');
const Case = require('../models/Case');
const { Client, Environment } = require('square');

// Square client configuration
const squareClient = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN,
  environment: process.env.SQUARE_ENVIRONMENT === 'production' ? Environment.Production : Environment.Sandbox
});

// Helper function to ensure case has required fields
async function ensureCaseFields(caseData, userId) {
  // Ensure attorney is set
  if (!caseData.attorney) {
    caseData.attorney = userId;
  }
  
  // Ensure caseType is set
  if (!caseData.caseType) {
    caseData.caseType = 'other';
  }
  
  // Ensure clientName is set
  if (!caseData.clientName || caseData.clientName.trim() === '') {
    caseData.clientName = 'Unknown Client';
  }
  
  // Ensure caseName is set
  if (!caseData.caseName || caseData.caseName.trim() === '') {
    caseData.caseName = `Case for ${caseData.clientName}`;
  }
  
  // Ensure caseNumber is set
  if (!caseData.caseNumber || caseData.caseNumber.trim() === '') {
    const count = await Case.countDocuments();
    caseData.caseNumber = `CASE-${String(count + 1).padStart(6, '0')}`;
  }
  
  return caseData;
}

// Get all payments
router.get('/', auth, async (req, res) => {
  try {
    const { caseId, status, page = 1, limit = 10 } = req.query;
    const query = { status: { $ne: 'deleted' } };
    
    if (caseId) query.caseId = caseId;
    if (status && status !== 'all') query.status = status;

    const payments = await Payment.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('processedBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Payment.countDocuments(query);

    res.json({
      payments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Error getting payments:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Process Square payment
router.post('/square', auth, async (req, res) => {
  try {
    const { caseId, amount, sourceId, description, paidBy } = req.body;

    // Verify case exists and ensure required fields
    let caseData = await Case.findById(caseId);
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    // Ensure case has all required fields
    caseData = await ensureCaseFields(caseData, req.user._id);

    // Process payment with Square
    const paymentResponse = await squareClient.paymentsApi.createPayment({
      sourceId: sourceId,
      idempotencyKey: `${caseId}-${Date.now()}`,
      amountMoney: {
        amount: Math.round(amount * 100),
        currency: 'USD'
      }
    });

    if (paymentResponse.result.payment.status === 'COMPLETED') {
      // Create payment record
      const payment = new Payment({
        caseId,
        amount,
        paymentMethod: 'square',
        squarePaymentId: paymentResponse.result.payment.id,
        description,
        paidBy,
        processedBy: req.user._id
      });

      await payment.save();

      // Update case totals
      caseData.totalPaid += amount;
      await caseData.save();

      res.json({ 
        message: 'Payment processed successfully',
        payment: payment
      });
    } else {
      res.status(400).json({ message: 'Payment failed' });
    }
  } catch (error) {
    console.error('Error processing Square payment:', error);
    if (error.errors) {
      res.status(400).json({ message: error.errors[0].detail });
    } else {
      res.status(500).json({ message: 'Server error' });
    }
  }
});

// Record manual payment
router.post('/manual', auth, async (req, res) => {
  try {
    const { caseId, amount, paymentMethod, description, paidBy, notes } = req.body;

    // Verify case exists and ensure required fields
    let caseData = await Case.findById(caseId);
    if (!caseData) {
      return res.status(404).json({ message: 'Case not found' });
    }

    // Ensure case has all required fields
    caseData = await ensureCaseFields(caseData, req.user._id);

    // Create payment record
    const payment = new Payment({
      caseId,
      amount,
      paymentMethod,
      description,
      paidBy,
      notes,
      processedBy: req.user._id
    });

    await payment.save();

    // Update case totals
    caseData.totalPaid += amount;
    await caseData.save();

    res.json({ 
      message: 'Payment recorded successfully',
      payment: payment
    });
  } catch (error) {
    console.error('Error recording manual payment:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get payment by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('processedBy', 'firstName lastName');

    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }

    res.json(payment);
  } catch (error) {
    console.error('Error getting payment:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete payment (soft delete)
router.delete('/:id', auth, async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id);
    
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }

    // Update case totals
    if (payment.caseId) {
      const caseData = await Case.findById(payment.caseId);
      if (caseData) {
        caseData.totalPaid -= payment.amount;
        // Ensure all required fields are present before saving
        await ensureCaseFields(caseData, req.user._id);
        await caseData.save();
      }
    }

    // Soft delete - update status instead of removing
    await Payment.findByIdAndUpdate(req.params.id, { status: 'deleted' }, { new: true });

    res.json({ message: 'Payment deleted successfully' });
  } catch (error) {
    console.error('Error deleting payment:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update payment
router.put('/:id', auth, async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id);
    
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }

    // Update case totals
    if (payment.caseId) {
      const caseData = await Case.findById(payment.caseId);
      if (caseData) {
        caseData.totalPaid = caseData.totalPaid - payment.amount + req.body.amount;
        // Ensure all required fields are present before saving
        await ensureCaseFields(caseData, req.user._id);
        await caseData.save();
      }
    }

    const updatedPayment = await Payment.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    ).populate('caseId', 'caseNumber caseName clientName')
     .populate('processedBy', 'firstName lastName');

    res.json(updatedPayment);
  } catch (error) {
    console.error('Error updating payment:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;