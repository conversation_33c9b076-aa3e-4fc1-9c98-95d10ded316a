const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const Case = require('../models/Case');
const User = require('../models/User');
const { auth } = require('../middleware/auth');
const { cleanObjectIds, validateRequiredFields } = require('../middleware/validation');

// GET /api/tasks - Get all tasks with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const priority = req.query.priority;
    const caseId = req.query.caseId;
    const assignedTo = req.query.assignedTo;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    const search = req.query.search;

    // Build query
    const query = { archived: { $ne: true } };
    
    if (status) query.status = status;
    if (priority) query.priority = priority;
    if (caseId) query.caseId = caseId;
    if (assignedTo) query.assignedTo = assignedTo;
    
    if (startDate || endDate) {
      query.dueDate = {};
      if (startDate) query.dueDate.$gte = new Date(startDate);
      if (endDate) query.dueDate.$lte = new Date(endDate);
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const tasks = await Task.find(query)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('assignedTo', 'firstName lastName')
      .populate('assignedBy', 'firstName lastName')
      .populate('completedBy', 'firstName lastName')
      .sort({ dueDate: 1, priority: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Task.countDocuments(query);

    // Calculate summary stats
    const statusSummary = await Task.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const prioritySummary = await Task.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$priority',
          count: { $sum: 1 }
        }
      }
    ]);

    // Overdue tasks count
    const overdueCount = await Task.countDocuments({
      ...query,
      dueDate: { $lt: new Date() },
      status: { $nin: ['completed', 'cancelled'] }
    });

    res.json({
      tasks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      summary: {
        statusBreakdown: statusSummary,
        priorityBreakdown: prioritySummary,
        overdueCount
      }
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/tasks/:id - Get single task
router.get('/:id', auth, async (req, res) => {
  try {
    const task = await Task.findById(req.params.id)
      .populate('caseId', 'caseNumber caseName clientName')
      .populate('assignedTo', 'firstName lastName email')
      .populate('assignedBy', 'firstName lastName')
      .populate('completedBy', 'firstName lastName');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/tasks - Create new task
router.post('/', auth, cleanObjectIds, validateRequiredFields(['name', 'caseId']), async (req, res) => {
  try {
    const taskData = {
      ...req.body,
      assignedBy: req.user.id,
      assignedTo: req.body.assignedTo || req.user.id
    };

    const task = new Task(taskData);
    await task.save();

    // Populate the response
    await task.populate('caseId', 'caseNumber caseName clientName');
    await task.populate('assignedTo', 'firstName lastName');
    await task.populate('assignedBy', 'firstName lastName');

    res.status(201).json(task);
  } catch (error) {
    console.error('Error creating task:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/tasks/:id - Update task
router.put('/:id', auth, cleanObjectIds, async (req, res) => {
  try {
    const updateData = { ...req.body };
    
    // Auto-set completion details if marking as completed
    if (updateData.status === 'completed' && !updateData.completedAt) {
      updateData.completedAt = new Date();
      updateData.completedBy = req.user.id;
    }

    const task = await Task.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('assignedTo', 'firstName lastName')
    .populate('assignedBy', 'firstName lastName')
    .populate('completedBy', 'firstName lastName');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json(task);
  } catch (error) {
    console.error('Error updating task:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/tasks/:id - Delete task
router.delete('/:id', auth, async (req, res) => {
  try {
    const task = await Task.findByIdAndDelete(req.params.id);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/tasks/:id/status - Update task status
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { status } = req.body;
    
    const updateData = { status };
    
    // Set completion details if marking as completed
    if (status === 'completed') {
      updateData.completedAt = new Date();
      updateData.completedBy = req.user.id;
    }

    const task = await Task.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    )
    .populate('caseId', 'caseNumber caseName clientName')
    .populate('assignedTo', 'firstName lastName')
    .populate('completedBy', 'firstName lastName');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json(task);
  } catch (error) {
    console.error('Error updating task status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/tasks/:id/subtasks - Add subtask
router.post('/:id/subtasks', auth, async (req, res) => {
  try {
    const { name } = req.body;
    
    const task = await Task.findById(req.params.id);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    task.subtasks.push({
      name,
      completed: false
    });

    await task.save();
    
    await task.populate('caseId', 'caseNumber caseName clientName');
    await task.populate('assignedTo', 'firstName lastName');

    res.json(task);
  } catch (error) {
    console.error('Error adding subtask:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/tasks/:id/subtasks/:subtaskId - Update subtask
router.put('/:id/subtasks/:subtaskId', auth, async (req, res) => {
  try {
    const { completed } = req.body;
    
    const task = await Task.findById(req.params.id);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const subtask = task.subtasks.id(req.params.subtaskId);
    if (!subtask) {
      return res.status(404).json({ message: 'Subtask not found' });
    }

    subtask.completed = completed;
    if (completed) {
      subtask.completedAt = new Date();
      subtask.completedBy = req.user.id;
    } else {
      subtask.completedAt = null;
      subtask.completedBy = null;
    }

    await task.save();
    
    await task.populate('caseId', 'caseNumber caseName clientName');
    await task.populate('assignedTo', 'firstName lastName');

    res.json(task);
  } catch (error) {
    console.error('Error updating subtask:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;