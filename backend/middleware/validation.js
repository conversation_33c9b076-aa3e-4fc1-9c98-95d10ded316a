const mongoose = require('mongoose');

// Middleware to clean and validate ObjectId fields
const cleanObjectIds = (req, res, next) => {
  // List of fields that should be ObjectIds
  const objectIdFields = ['caseId', 'userId', 'assignedTo', 'assignedBy', 'completedBy', 'invoiceId', 'user', 'createdBy', 'attorney', 'caseManager'];
  
  // Clean request body
  if (req.body) {
    objectIdFields.forEach(field => {
      if (req.body[field] === '' || req.body[field] === null || req.body[field] === undefined) {
        delete req.body[field];
      } else if (req.body[field] && typeof req.body[field] === 'string') {
        // Validate ObjectId format
        if (!mongoose.Types.ObjectId.isValid(req.body[field])) {
          return res.status(400).json({ 
            message: `Invalid ${field} format`,
            field: field,
            value: req.body[field]
          });
        }
      }
    });
  }
  
  // Clean query parameters
  if (req.query) {
    objectIdFields.forEach(field => {
      if (req.query[field] === '' || req.query[field] === null || req.query[field] === undefined) {
        delete req.query[field];
      } else if (req.query[field] && typeof req.query[field] === 'string') {
        // Validate ObjectId format
        if (!mongoose.Types.ObjectId.isValid(req.query[field])) {
          return res.status(400).json({ 
            message: `Invalid ${field} format in query`,
            field: field,
            value: req.query[field]
          });
        }
      }
    });
  }
  
  next();
};

// Middleware to validate required fields
const validateRequiredFields = (requiredFields) => {
  return (req, res, next) => {
    const missingFields = [];
    
    requiredFields.forEach(field => {
      if (!req.body[field] || (typeof req.body[field] === 'string' && req.body[field].trim() === '')) {
        missingFields.push(field);
      }
    });
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        message: 'Missing required fields',
        missingFields: missingFields
      });
    }
    
    next();
  };
};

module.exports = {
  cleanObjectIds,
  validateRequiredFields
};
