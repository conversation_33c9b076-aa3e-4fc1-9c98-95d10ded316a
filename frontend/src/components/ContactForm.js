import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import contactService from '../services/contactService';

const ContactForm = ({ contact, onSave, onCancel }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    company: '',
    jobTitle: '',
    email: '',
    phones: {
      home: '',
      work: '',
      mobile: '',
      fax: ''
    },
    homeAddress: {
      street: '',
      street2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'USA'
    },
    workAddress: {
      street: '',
      street2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'USA'
    },
    webPage: '',
    birthday: '',
    contactGroup: '',
    licenseNumber: '',
    licenseState: '',
    privateNotes: '',
    loginEnabled: false,
    archived: false
  });

  useEffect(() => {
    if (contact) {
      setFormData({
        firstName: contact.firstName || '',
        middleName: contact.middleName || '',
        lastName: contact.lastName || '',
        company: contact.company || '',
        jobTitle: contact.jobTitle || '',
        email: contact.email || '',
        phones: {
          home: contact.phones?.home || '',
          work: contact.phones?.work || '',
          mobile: contact.phones?.mobile || '',
          fax: contact.phones?.fax || ''
        },
        homeAddress: {
          street: contact.homeAddress?.street || '',
          street2: contact.homeAddress?.street2 || '',
          city: contact.homeAddress?.city || '',
          state: contact.homeAddress?.state || '',
          postalCode: contact.homeAddress?.postalCode || '',
          country: contact.homeAddress?.country || 'USA'
        },
        workAddress: {
          street: contact.workAddress?.street || '',
          street2: contact.workAddress?.street2 || '',
          city: contact.workAddress?.city || '',
          state: contact.workAddress?.state || '',
          postalCode: contact.workAddress?.postalCode || '',
          country: contact.workAddress?.country || 'USA'
        },
        webPage: contact.webPage || '',
        birthday: contact.birthday ? contact.birthday.split('T')[0] : '',
        contactGroup: contact.contactGroup || '',
        licenseNumber: contact.licenseNumber || '',
        licenseState: contact.licenseState || '',
        privateNotes: contact.privateNotes || '',
        loginEnabled: contact.loginEnabled || false,
        archived: contact.archived || false
      });
    }
  }, [contact]);

  const handleChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    try {
      const contactData = {
        ...formData,
        birthday: formData.birthday ? new Date(formData.birthday) : null
      };

      let savedContact;
      if (contact) {
        savedContact = await contactService.updateContact(contact._id, contactData);
      } else {
        savedContact = await contactService.createContact(contactData);
      }

      onSave(savedContact);
    } catch (error) {
      console.error('Error saving contact:', error);
      // Handle error (maybe show a snackbar)
    } finally {
      setLoading(false);
    }
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {formData.company ? <BusinessIcon /> : <PersonIcon />}
          {contact ? 'Edit Contact' : 'Add New Contact'}
        </Box>
      </DialogTitle>

      <DialogContent sx={{ minHeight: 500 }}>
        <Paper sx={{ width: '100%' }}>
          <Tabs 
            value={activeTab} 
            onChange={(e, newValue) => setActiveTab(newValue)}
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Basic Info" />
            <Tab label="Contact Details" />
            <Tab label="Addresses" />
            <Tab label="Professional" />
            <Tab label="Notes & Settings" />
          </Tabs>

          {/* Basic Information Tab */}
          <TabPanel value={activeTab} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={formData.firstName}
                  onChange={handleChange('firstName')}
                  required={!formData.company}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={formData.lastName}
                  onChange={handleChange('lastName')}
                  required={!formData.company}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Middle Name"
                  value={formData.middleName}
                  onChange={handleChange('middleName')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Company"
                  value={formData.company}
                  onChange={handleChange('company')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Job Title"
                  value={formData.jobTitle}
                  onChange={handleChange('jobTitle')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Contact Type</InputLabel>
                  <Select
                    value={formData.contactGroup}
                    onChange={handleChange('contactGroup')}
                    label="Contact Type"
                  >
                    <MenuItem value="Client">Client</MenuItem>
                    <MenuItem value="Attorney">Attorney</MenuItem>
                    <MenuItem value="Vendor">Vendor</MenuItem>
                    <MenuItem value="Expert">Expert</MenuItem>
                    <MenuItem value="Court">Court Personnel</MenuItem>
                    <MenuItem value="Insurance">Insurance</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Birthday"
                  type="date"
                  value={formData.birthday}
                  onChange={handleChange('birthday')}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Contact Details Tab */}
          <TabPanel value={activeTab} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Contact Information
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Email Address"
                  type="email"
                  value={formData.email}
                  onChange={handleChange('email')}
                  InputProps={{
                    startAdornment: <EmailIcon sx={{ mr: 1, color: 'action.active' }} />
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Mobile Phone"
                  value={formData.phones.mobile}
                  onChange={handleChange('phones.mobile')}
                  InputProps={{
                    startAdornment: <PhoneIcon sx={{ mr: 1, color: 'action.active' }} />
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Home Phone"
                  value={formData.phones.home}
                  onChange={handleChange('phones.home')}
                  InputProps={{
                    startAdornment: <HomeIcon sx={{ mr: 1, color: 'action.active' }} />
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Work Phone"
                  value={formData.phones.work}
                  onChange={handleChange('phones.work')}
                  InputProps={{
                    startAdornment: <WorkIcon sx={{ mr: 1, color: 'action.active' }} />
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Fax"
                  value={formData.phones.fax}
                  onChange={handleChange('phones.fax')}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Website"
                  value={formData.webPage}
                  onChange={handleChange('webPage')}
                  placeholder="https://example.com"
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Addresses Tab */}
          <TabPanel value={activeTab} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Home Address
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Street Address"
                  value={formData.homeAddress.street}
                  onChange={handleChange('homeAddress.street')}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Street Address 2"
                  value={formData.homeAddress.street2}
                  onChange={handleChange('homeAddress.street2')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="City"
                  value={formData.homeAddress.city}
                  onChange={handleChange('homeAddress.city')}
                />
              </Grid>
              
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="State"
                  value={formData.homeAddress.state}
                  onChange={handleChange('homeAddress.state')}
                />
              </Grid>
              
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="ZIP Code"
                  value={formData.homeAddress.postalCode}
                  onChange={handleChange('homeAddress.postalCode')}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Work Address
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Street Address"
                  value={formData.workAddress.street}
                  onChange={handleChange('workAddress.street')}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Street Address 2"
                  value={formData.workAddress.street2}
                  onChange={handleChange('workAddress.street2')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="City"
                  value={formData.workAddress.city}
                  onChange={handleChange('workAddress.city')}
                />
              </Grid>
              
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="State"
                  value={formData.workAddress.state}
                  onChange={handleChange('workAddress.state')}
                />
              </Grid>
              
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="ZIP Code"
                  value={formData.workAddress.postalCode}
                  onChange={handleChange('workAddress.postalCode')}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Professional Tab */}
          <TabPanel value={activeTab} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Professional Information
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="License Number"
                  value={formData.licenseNumber}
                  onChange={handleChange('licenseNumber')}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="License State"
                  value={formData.licenseState}
                  onChange={handleChange('licenseState')}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Notes & Settings Tab */}
          <TabPanel value={activeTab} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Notes & Settings
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Private Notes"
                  multiline
                  rows={4}
                  value={formData.privateNotes}
                  onChange={handleChange('privateNotes')}
                  placeholder="Internal notes about this contact..."
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.loginEnabled}
                      onChange={handleChange('loginEnabled')}
                    />
                  }
                  label="Allow portal login"
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.archived}
                      onChange={handleChange('archived')}
                    />
                  }
                  label="Archived"
                />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="submit" 
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Saving...' : (contact ? 'Update Contact' : 'Create Contact')}
        </Button>
      </DialogActions>
    </Box>
  );
};

export default ContactForm;