import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Divider
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { addDays } from 'date-fns';
import invoiceService from '../services/invoiceService';
import { caseService } from '../services/caseService';
import { useAuth } from '../contexts/AuthContext';

const InvoiceForm = ({ invoice, onSave, onCancel }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [cases, setCases] = useState([]);
  const [formData, setFormData] = useState({
    invoiceNumber: '',
    caseId: '',
    invoiceDate: new Date(),
    dueDate: addDays(new Date(), 30),
    billingUser: user?.id || '',
    status: 'draft',
    paymentTerms: 'Net 30',
    notes: '',
    termsAndConditions: '',
    // Financial fields
    timeEntryTotal: 0,
    expenseTotal: 0,
    flatFeeTotal: 0,
    discountTotal: 0,
    writeOffTotal: 0,
    additionTotal: 0,
    balanceForwardTotal: 0,
    // Client address
    clientAddress: {
      street: '',
      street2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'USA'
    }
  });

  const statusOptions = [
    { value: 'draft', label: 'Draft' },
    { value: 'sent', label: 'Sent' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const paymentTermsOptions = [
    'Net 30',
    'Net 15',
    'Net 10',
    'Due on Receipt',
    'Net 60'
  ];

  useEffect(() => {
    fetchCases();
    
    if (invoice) {
      setFormData({
        invoiceNumber: invoice.invoiceNumber || '',
        caseId: invoice.caseId?._id || '',
        invoiceDate: new Date(invoice.invoiceDate),
        dueDate: new Date(invoice.dueDate),
        billingUser: invoice.billingUser?._id || user?.id || '',
        status: invoice.status || 'draft',
        paymentTerms: invoice.paymentTerms || 'Net 30',
        notes: invoice.notes || '',
        termsAndConditions: invoice.termsAndConditions || '',
        timeEntryTotal: invoice.timeEntryTotal || 0,
        expenseTotal: invoice.expenseTotal || 0,
        flatFeeTotal: invoice.flatFeeTotal || 0,
        discountTotal: invoice.discountTotal || 0,
        writeOffTotal: invoice.writeOffTotal || 0,
        additionTotal: invoice.additionTotal || 0,
        balanceForwardTotal: invoice.balanceForwardTotal || 0,
        clientAddress: {
          street: invoice.clientAddress?.street || '',
          street2: invoice.clientAddress?.street2 || '',
          city: invoice.clientAddress?.city || '',
          state: invoice.clientAddress?.state || '',
          postalCode: invoice.clientAddress?.postalCode || '',
          country: invoice.clientAddress?.country || 'USA'
        }
      });
    }
  }, [invoice, user]);

  const fetchCases = async () => {
    try {
      const data = await caseService.getCases({ limit: 100 });
      setCases(data.cases || []);
    } catch (error) {
      console.error('Error fetching cases:', error);
    }
  };

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleDateChange = (field) => (newDate) => {
    setFormData(prev => ({
      ...prev,
      [field]: newDate
    }));

    // Auto-calculate due date when invoice date changes
    if (field === 'invoiceDate' && formData.paymentTerms.includes('Net')) {
      const days = parseInt(formData.paymentTerms.replace('Net ', ''));
      setFormData(prev => ({
        ...prev,
        dueDate: addDays(newDate, days)
      }));
    }
  };

  const handleCaseChange = (event, newValue) => {
    setFormData(prev => ({
      ...prev,
      caseId: newValue?._id || ''
    }));
  };

  const handlePaymentTermsChange = (event) => {
    const newTerms = event.target.value;
    setFormData(prev => ({
      ...prev,
      paymentTerms: newTerms
    }));

    // Auto-update due date based on payment terms
    if (newTerms.includes('Net')) {
      const days = parseInt(newTerms.replace('Net ', ''));
      setFormData(prev => ({
        ...prev,
        dueDate: addDays(prev.invoiceDate, days)
      }));
    } else if (newTerms === 'Due on Receipt') {
      setFormData(prev => ({
        ...prev,
        dueDate: prev.invoiceDate
      }));
    }
  };

  const calculateTotal = () => {
    const subtotal = 
      parseFloat(formData.timeEntryTotal || 0) + 
      parseFloat(formData.expenseTotal || 0) + 
      parseFloat(formData.flatFeeTotal || 0);

    return subtotal - 
      parseFloat(formData.discountTotal || 0) - 
      parseFloat(formData.writeOffTotal || 0) + 
      parseFloat(formData.additionTotal || 0) + 
      parseFloat(formData.balanceForwardTotal || 0);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        ...formData,
        invoiceDate: formData.invoiceDate.toISOString(),
        dueDate: formData.dueDate.toISOString(),
        totalAmount: calculateTotal()
      };

      let savedInvoice;
      if (invoice) {
        savedInvoice = await invoiceService.updateInvoice(invoice._id, submitData);
      } else {
        savedInvoice = await invoiceService.createInvoice(submitData);
      }

      onSave(savedInvoice);
    } catch (error) {
      console.error('Error saving invoice:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box component="form" onSubmit={handleSubmit}>
        <DialogTitle>
          {invoice ? 'Edit Invoice' : 'Create Invoice'}
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Invoice Information
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Invoice Number"
                value={formData.invoiceNumber}
                onChange={handleChange('invoiceNumber')}
                placeholder="Auto-generated if left blank"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={handleChange('status')}
                  label="Status"
                >
                  {statusOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                options={cases}
                getOptionLabel={(option) => `${option.caseNumber} - ${option.caseName}`}
                value={cases.find(c => c._id === formData.caseId) || null}
                onChange={handleCaseChange}
                renderInput={(params) => (
                  <TextField {...params} label="Case" required />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <DatePicker
                label="Invoice Date"
                value={formData.invoiceDate}
                onChange={handleDateChange('invoiceDate')}
                renderInput={(params) => (
                  <TextField {...params} fullWidth required />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Payment Terms</InputLabel>
                <Select
                  value={formData.paymentTerms}
                  onChange={handlePaymentTermsChange}
                  label="Payment Terms"
                >
                  {paymentTermsOptions.map((term) => (
                    <MenuItem key={term} value={term}>
                      {term}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={4}>
              <DatePicker
                label="Due Date"
                value={formData.dueDate}
                onChange={handleDateChange('dueDate')}
                renderInput={(params) => (
                  <TextField {...params} fullWidth required />
                )}
              />
            </Grid>

            {/* Financial Information */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Financial Details
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Time Entries Total"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.timeEntryTotal}
                onChange={handleChange('timeEntryTotal')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Expenses Total"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.expenseTotal}
                onChange={handleChange('expenseTotal')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Flat Fees Total"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.flatFeeTotal}
                onChange={handleChange('flatFeeTotal')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Discount Total"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.discountTotal}
                onChange={handleChange('discountTotal')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Write Off Total"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.writeOffTotal}
                onChange={handleChange('writeOffTotal')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Additional Charges"
                type="number"
                inputProps={{ step: 0.01 }}
                value={formData.additionTotal}
                onChange={handleChange('additionTotal')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="h6" align="right">
                  Total Amount: {formatCurrency(calculateTotal())}
                </Typography>
              </Box>
            </Grid>

            {/* Notes */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Additional Information
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={handleChange('notes')}
                placeholder="Internal notes..."
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Terms and Conditions"
                multiline
                rows={3}
                value={formData.termsAndConditions}
                onChange={handleChange('termsAndConditions')}
                placeholder="Terms and conditions for this invoice..."
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (invoice ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Box>
    </LocalizationProvider>
  );
};

export default InvoiceForm;