import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useAuth } from '../contexts/AuthContext';

const LeadForm = ({ lead, onSave, onCancel }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Basic Info
    firstName: '',
    middleName: '',
    lastName: '',
    name: '',
    
    // Contact
    contact: {
      email: '',
      workPhone: '',
      homePhone: '',
      cellPhone: ''
    },
    
    // Address
    address: {
      street: '',
      street2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'USA'
    },
    
    // Lead Details
    practiceArea: '',
    caseType: 'other',
    status: 'new',
    source: 'other',
    details: '',
    value: 0,
    referredBy: '',
    
    // Dates
    birthday: null,
    followUpDate: null,
    
    // License
    licenseNumber: '',
    licenseState: '',
    
    // Case Info
    potentialCaseName: '',
    potentialCaseDescription: '',
    conflictCheck: false,
    conflictCheckNotes: '',
    
    // Notes
    notes: ''
  });

  const statuses = [
    { value: 'new', label: 'New' },
    { value: 'contacted', label: 'Contacted' },
    { value: 'qualified', label: 'Qualified' },
    { value: 'converted', label: 'Converted' },
    { value: 'closed', label: 'Closed' }
  ];

  const caseTypes = [
    { value: 'personal_injury', label: 'Personal Injury' },
    { value: 'criminal_defense', label: 'Criminal Defense' },
    { value: 'family_law', label: 'Family Law' },
    { value: 'family_law_tx', label: 'Family Law TX' },
    { value: 'family_law_il', label: 'Family Law IL' },
    { value: 'civil_litigation', label: 'Civil Litigation' },
    { value: 'business_law', label: 'Business Law' },
    { value: 'estate_planning', label: 'Estate Planning' },
    { value: 'immigration', label: 'Immigration' },
    { value: 'employment_law', label: 'Employment Law' },
    { value: 'real_estate', label: 'Real Estate' },
    { value: 'other', label: 'Other' }
  ];

  const sources = [
    { value: 'website', label: 'Website' },
    { value: 'referral', label: 'Referral' },
    { value: 'advertising', label: 'Advertising' },
    { value: 'social_media', label: 'Social Media' },
    { value: 'google', label: 'Google' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    if (lead) {
      setFormData({
        firstName: lead.firstName || '',
        middleName: lead.middleName || '',
        lastName: lead.lastName || '',
        name: lead.name || '',
        contact: {
          email: lead.contact?.email || '',
          workPhone: lead.contact?.workPhone || '',
          homePhone: lead.contact?.homePhone || '',
          cellPhone: lead.contact?.cellPhone || ''
        },
        address: {
          street: lead.address?.street || '',
          street2: lead.address?.street2 || '',
          city: lead.address?.city || '',
          state: lead.address?.state || '',
          postalCode: lead.address?.postalCode || '',
          country: lead.address?.country || 'USA'
        },
        practiceArea: lead.practiceArea || '',
        caseType: lead.caseType || 'other',
        status: lead.status || 'new',
        source: lead.source || 'other',
        details: lead.details || '',
        value: lead.value || 0,
        referredBy: lead.referredBy || '',
        birthday: lead.birthday ? new Date(lead.birthday) : null,
        followUpDate: lead.followUpDate ? new Date(lead.followUpDate) : null,
        licenseNumber: lead.licenseNumber || '',
        licenseState: lead.licenseState || '',
        potentialCaseName: lead.potentialCaseName || '',
        potentialCaseDescription: lead.potentialCaseDescription || '',
        conflictCheck: lead.conflictCheck || false,
        conflictCheckNotes: lead.conflictCheckNotes || '',
        notes: lead.notes || ''
      });
    }
  }, [lead]);

  // Auto-update name when first/last name changes
  useEffect(() => {
    const fullName = [formData.firstName, formData.middleName, formData.lastName]
      .filter(n => n.trim())
      .join(' ')
      .trim();
    
    if (fullName !== formData.name) {
      setFormData(prev => ({
        ...prev,
        name: fullName
      }));
    }
  }, [formData.firstName, formData.middleName, formData.lastName]);

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleDateChange = (field) => (newDate) => {
    setFormData(prev => ({
      ...prev,
      [field]: newDate
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        ...formData,
        birthday: formData.birthday ? formData.birthday.toISOString() : null,
        followUpDate: formData.followUpDate ? formData.followUpDate.toISOString() : null,
        value: parseFloat(formData.value) || 0
      };

      onSave(submitData);
    } catch (error) {
      console.error('Error saving lead:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box component="form" onSubmit={handleSubmit}>
        <DialogTitle>
          {lead ? 'Edit Lead' : 'Add Lead'}
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Contact Information
              </Typography>
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={handleChange('firstName')}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Middle Name"
                value={formData.middleName}
                onChange={handleChange('middleName')}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={handleChange('lastName')}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.contact.email}
                onChange={handleChange('contact.email')}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Cell Phone"
                value={formData.contact.cellPhone}
                onChange={handleChange('contact.cellPhone')}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Work Phone"
                value={formData.contact.workPhone}
                onChange={handleChange('contact.workPhone')}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Home Phone"
                value={formData.contact.homePhone}
                onChange={handleChange('contact.homePhone')}
              />
            </Grid>

            {/* Lead Details */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Lead Details
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Practice Area</InputLabel>
                <Select
                  value={formData.caseType}
                  onChange={handleChange('caseType')}
                  label="Practice Area"
                  required
                >
                  {caseTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={handleChange('status')}
                  label="Status"
                >
                  {statuses.map((status) => (
                    <MenuItem key={status.value} value={status.value}>
                      {status.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Lead Source</InputLabel>
                <Select
                  value={formData.source}
                  onChange={handleChange('source')}
                  label="Lead Source"
                >
                  {sources.map((source) => (
                    <MenuItem key={source.value} value={source.value}>
                      {source.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Estimated Value"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.value}
                onChange={handleChange('value')}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Details"
                multiline
                rows={4}
                value={formData.details}
                onChange={handleChange('details')}
                placeholder="Describe the potential case details..."
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Potential Case Name"
                value={formData.potentialCaseName}
                onChange={handleChange('potentialCaseName')}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Referred By"
                value={formData.referredBy}
                onChange={handleChange('referredBy')}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={handleChange('notes')}
                placeholder="Internal notes about this lead..."
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (lead ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Box>
    </LocalizationProvider>
  );
};

export default LeadForm;