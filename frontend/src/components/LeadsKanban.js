import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Chip,
  Avatar,
  IconButton,
  Button,
  Alert
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Add as AddIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { leadService } from '../services/leadService';

// Sortable Lead Card Component
const SortableLeadCard = ({ lead, onActionClick }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: lead._id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getStatusColor = (status) => {
    const colors = {
      new: '#2196f3',
      contacted: '#ff9800', 
      qualified: '#4caf50',
      converted: '#8bc34a',
      closed: '#9e9e9e'
    };
    return colors[status] || '#9e9e9e';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      sx={{
        mb: 1,
        cursor: isDragging ? 'grabbing' : 'grab',
        '&:hover': {
          boxShadow: 2
        }
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Typography variant="body2" fontWeight="medium" sx={{ flex: 1 }}>
            {lead.name}
          </Typography>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onActionClick(e, lead);
            }}
            sx={{ ml: 1 }}
          >
            <MoreVertIcon fontSize="small" />
          </IconButton>
        </Box>
        
        <Box sx={{ mb: 1 }}>
          <Chip
            label={lead.status}
            size="small"
            sx={{
              bgcolor: getStatusColor(lead.status),
              color: 'white',
              fontSize: '0.7rem'
            }}
          />
        </Box>
        
        <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mb: 0.5 }}>
          {lead.contact?.email || 'No email'}
        </Typography>
        
        <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mb: 0.5 }}>
          {lead.contact?.cellPhone || lead.contact?.workPhone || 'No phone'}
        </Typography>
        
        <Typography variant="body2" fontWeight="medium" color="primary">
          {formatCurrency(lead.value)}
        </Typography>
        
        {lead.potentialCaseName && (
          <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 0.5 }}>
            {lead.potentialCaseName}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

// Staff Column Component
const StaffColumn = ({ staff, onActionClick, onRefresh }) => {
  return (
    <Paper sx={{ p: 2, minHeight: 400, width: 300, mr: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: '0.8rem' }}>
              {staff.staffName.split(' ').map(n => n[0]).join('')}
            </Avatar>
            {staff.staffName}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {staff.count} leads • {staff.converted} converted
          </Typography>
        </Box>
        <IconButton size="small" onClick={onRefresh}>
          <RefreshIcon fontSize="small" />
        </IconButton>
      </Box>
      
      <SortableContext items={staff.leads.map(lead => lead._id)} strategy={verticalListSortingStrategy}>
        <Box sx={{ minHeight: 300 }}>
          {staff.leads.map((lead) => (
            <SortableLeadCard
              key={lead._id}
              lead={lead}
              onActionClick={onActionClick}
            />
          ))}
          {staff.leads.length === 0 && (
            <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', mt: 4 }}>
              No leads assigned
            </Typography>
          )}
        </Box>
      </SortableContext>
    </Paper>
  );
};

// Main Kanban Component
const LeadsKanban = ({ onAddLead, onEditLead, onDeleteLead, onStatusChange }) => {
  const [staffLeads, setStaffLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    fetchStaffLeads();
  }, []);

  const fetchStaffLeads = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await leadService.getStaffLeads();
      setStaffLeads(data.staffLeads || []);
    } catch (error) {
      console.error('Error fetching staff leads:', error);
      setError('Failed to load staff leads');
    } finally {
      setLoading(false);
    }
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;
    
    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    // Find which staff member's column the items are in
    let sourceStaff = null;
    let sourceIndex = -1;
    
    for (const staff of staffLeads) {
      const index = staff.leads.findIndex(lead => lead._id === activeId);
      if (index !== -1) {
        sourceStaff = staff;
        sourceIndex = index;
        break;
      }
    }

    if (!sourceStaff) return;

    const targetIndex = sourceStaff.leads.findIndex(lead => lead._id === overId);
    if (targetIndex === -1) return;

    // Update local state optimistically
    const newStaffLeads = staffLeads.map(staff => {
      if (staff.staffId === sourceStaff.staffId) {
        const newLeads = arrayMove(staff.leads, sourceIndex, targetIndex);
        // Update priorities based on new order
        newLeads.forEach((lead, index) => {
          lead.priority = newLeads.length - index;
        });
        return { ...staff, leads: newLeads };
      }
      return staff;
    });

    setStaffLeads(newStaffLeads);

    try {
      // Update priority on backend
      const leadToUpdate = sourceStaff.leads[sourceIndex];
      const newPriority = sourceStaff.leads.length - targetIndex;
      await leadService.updateLeadPriority(leadToUpdate._id, newPriority);
    } catch (error) {
      console.error('Error updating lead priority:', error);
      // Revert on error
      fetchStaffLeads();
    }
  };

  if (loading) {
    return <Typography>Loading leads kanban...</Typography>;
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Leads Kanban Board</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchStaffLeads}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onAddLead}
          >
            Add Lead
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <Box sx={{ display: 'flex', overflowX: 'auto', pb: 2 }}>
          {staffLeads.map((staff) => (
            <StaffColumn
              key={staff.staffId || 'unassigned'}
              staff={staff}
              onActionClick={(e, lead) => {
                // Handle action menu
                if (onEditLead) onEditLead(lead);
              }}
              onRefresh={fetchStaffLeads}
            />
          ))}
        </Box>
      </DndContext>
    </Box>
  );
};

export default LeadsKanban;