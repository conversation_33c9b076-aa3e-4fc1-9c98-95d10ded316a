import React from 'react';
import {
  App<PERSON><PERSON>,
  Tool<PERSON>,
  Ty<PERSON>graphy,
  Button,
  Box,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import {
  AccountCircle,
  Dashboard,
  People,
  Work,
  Payment,
  Assessment,
  SupervisorAccount,
  CalendarToday,
  Contacts,
  Schedule,
  Receipt,
  Description,
  Note,
  Assignment,
  Folder
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
  };

  const navItems = [
    { path: '/', label: 'Dashboard', icon: <Dashboard /> },
    { path: '/leads', label: 'Leads', icon: <People /> },
    { path: '/cases', label: 'Cases', icon: <Work /> },
    { path: '/contacts', label: 'Contacts', icon: <Contacts /> },
    { path: '/time-entries', label: 'Time', icon: <Schedule /> },
    { path: '/expenses', label: 'Expenses', icon: <Receipt /> },
    { path: '/invoices', label: 'Invoices', icon: <Description /> },
    { path: '/notes', label: 'Notes', icon: <Note /> },
    { path: '/tasks', label: 'Tasks', icon: <Assignment /> },
    { path: '/documents', label: 'Documents', icon: <Folder /> },
    { path: '/billing', label: 'Billing', icon: <Payment /> },
    { path: '/calendar', label: 'Calendar', icon: <CalendarToday /> },
    { path: '/reports', label: 'Reports', icon: <Assessment /> },
  ];

  if (user.role === 'admin') {
    navItems.push({
      path: '/employees',
      label: 'Employees',
      icon: <SupervisorAccount />
    });
  }

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          MyCase
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {navItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              startIcon={item.icon}
              onClick={() => navigate(item.path)}
              sx={{
                backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.1)' : 'transparent'
              }}
            >
              {item.label}
            </Button>
          ))}
          
          <IconButton
            size="large"
            onClick={handleMenu}
            color="inherit"
          >
            <AccountCircle />
          </IconButton>
          
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem disabled>
              {user.firstName} {user.lastName} ({user.role})
            </MenuItem>
            <MenuItem onClick={handleLogout}>Logout</MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;