import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Autocomplete
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import expenseService from '../services/expenseService';
import { caseService } from '../services/caseService';
import { useAuth } from '../contexts/AuthContext';

const ExpenseForm = ({ expense, onSave, onCancel }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [cases, setCases] = useState([]);
  const [formData, setFormData] = useState({
    date: new Date(),
    activity: '',
    description: '',
    quantity: 1,
    cost: '',
    total: '',
    category: 'other',
    caseId: '',
    user: user?.id || '',
    nonbillable: false,
    billed: false
  });

  const categories = [
    { value: 'travel', label: 'Travel' },
    { value: 'filing_fees', label: 'Filing Fees' },
    { value: 'copying', label: 'Copying' },
    { value: 'postage', label: 'Postage' },
    { value: 'research', label: 'Research' },
    { value: 'expert_fees', label: 'Expert Fees' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    fetchCases();
    
    if (expense) {
      setFormData({
        date: new Date(expense.date),
        activity: expense.activity || '',
        description: expense.description || '',
        quantity: expense.quantity || 1,
        cost: expense.cost || '',
        total: expense.total || '',
        category: expense.category || 'other',
        caseId: expense.caseId?._id || '',
        user: expense.user?._id || user?.id || '',
        nonbillable: expense.nonbillable || false,
        billed: expense.billed || false
      });
    }
  }, [expense, user]);

  // Calculate total when quantity or cost changes
  useEffect(() => {
    if (formData.quantity && formData.cost) {
      const calculatedTotal = parseFloat(formData.quantity) * parseFloat(formData.cost);
      if (calculatedTotal !== parseFloat(formData.total)) {
        setFormData(prev => ({
          ...prev,
          total: calculatedTotal.toFixed(2)
        }));
      }
    }
  }, [formData.quantity, formData.cost]);

  const fetchCases = async () => {
    try {
      const data = await caseService.getCases({ limit: 100 });
      setCases(data.cases || []);
    } catch (error) {
      console.error('Error fetching cases:', error);
    }
  };

  const handleChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (newDate) => {
    setFormData(prev => ({
      ...prev,
      date: newDate
    }));
  };

  const handleCaseChange = (event, newValue) => {
    setFormData(prev => ({
      ...prev,
      caseId: newValue?._id || ''
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        ...formData,
        quantity: parseFloat(formData.quantity),
        cost: parseFloat(formData.cost),
        total: parseFloat(formData.total),
        date: formData.date.toISOString()
      };

      let savedExpense;
      if (expense) {
        savedExpense = await expenseService.updateExpense(expense._id, submitData);
      } else {
        savedExpense = await expenseService.createExpense(submitData);
      }

      onSave(savedExpense);
    } catch (error) {
      console.error('Error saving expense:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box component="form" onSubmit={handleSubmit}>
        <DialogTitle>
          {expense ? 'Edit Expense' : 'Add Expense'}
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Date"
                value={formData.date}
                onChange={handleDateChange}
                renderInput={(params) => (
                  <TextField {...params} fullWidth required />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  onChange={handleChange('category')}
                  label="Category"
                  required
                >
                  {categories.map((category) => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                options={cases}
                getOptionLabel={(option) => `${option.caseNumber} - ${option.caseName}`}
                value={cases.find(c => c._id === formData.caseId) || null}
                onChange={handleCaseChange}
                renderInput={(params) => (
                  <TextField {...params} label="Case" required />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Activity"
                value={formData.activity}
                onChange={handleChange('activity')}
                placeholder="Brief description of the expense item..."
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={handleChange('description')}
                placeholder="Detailed description of the expense..."
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Quantity"
                type="number"
                inputProps={{ min: 0, step: 1 }}
                value={formData.quantity}
                onChange={handleChange('quantity')}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Unit Cost"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.cost}
                onChange={handleChange('cost')}
                InputProps={{
                  startAdornment: '$'
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Total Amount"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.total}
                onChange={handleChange('total')}
                InputProps={{
                  startAdornment: '$'
                }}
                helperText="Calculated automatically"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.nonbillable}
                    onChange={handleChange('nonbillable')}
                  />
                }
                label="Non-billable"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.billed}
                    onChange={handleChange('billed')}
                    disabled={formData.nonbillable}
                  />
                }
                label="Already Billed"
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (expense ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Box>
    </LocalizationProvider>
  );
};

export default ExpenseForm;