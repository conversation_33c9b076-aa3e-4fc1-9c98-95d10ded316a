import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  TextField,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Autocomplete
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import timeEntryService from '../services/timeEntryService';
import { caseService } from '../services/caseService';
import { useAuth } from '../contexts/AuthContext';

const TimeEntryForm = ({ timeEntry, onSave, onCancel }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [cases, setCases] = useState([]);
  const [formData, setFormData] = useState({
    date: new Date(),
    timeSpent: '', // Changed from 'hours'
    activity: '', // Added missing required field
    description: '',
    hourlyRate: '', // Changed from 'rate'
    total: '', // Changed from 'amount'
    nonbillable: false, // Changed from 'billable'
    billed: false,
    caseId: '', // Changed from 'case'
    user: user?.id || ''
  });

  useEffect(() => {
    fetchCases();
    
    if (timeEntry) {
      setFormData({
        date: new Date(timeEntry.date),
        timeSpent: timeEntry.timeSpent ? timeEntry.timeSpent / 60 : '', // Convert minutes to hours
        activity: timeEntry.activity || '',
        description: timeEntry.description || '',
        hourlyRate: timeEntry.hourlyRate || '',
        total: timeEntry.total || '',
        nonbillable: timeEntry.nonbillable || false,
        billed: timeEntry.billed || false,
        caseId: timeEntry.caseId?._id || timeEntry.case?._id || '',
        user: timeEntry.user?._id || user?.id || ''
      });
    } else {
      // Set default rate from user profile if available
      if (user?.hourlyRate) {
        setFormData(prev => ({
          ...prev,
          hourlyRate: user.hourlyRate
        }));
      }
    }
  }, [timeEntry, user]);

  // Calculate total when timeSpent or hourlyRate changes
  useEffect(() => {
    if (formData.timeSpent && formData.hourlyRate) {
      const calculatedTotal = parseFloat(formData.timeSpent) * parseFloat(formData.hourlyRate);
      if (calculatedTotal !== parseFloat(formData.total)) {
        setFormData(prev => ({
          ...prev,
          total: calculatedTotal.toFixed(2)
        }));
      }
    }
  }, [formData.timeSpent, formData.hourlyRate]);

  const fetchCases = async () => {
    try {
      const data = await caseService.getCases({ limit: 100 }); // Get more cases
      setCases(data.cases || []);
    } catch (error) {
      console.error('Error fetching cases:', error);
    }
  };

  const handleChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (newDate) => {
    setFormData(prev => ({
      ...prev,
      date: newDate
    }));
  };

  const handleCaseChange = (event, newValue) => {
    setFormData(prev => ({
      ...prev,
      caseId: newValue?._id || ''
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        ...formData,
        timeSpent: parseFloat(formData.timeSpent) * 60, // Convert hours to minutes
        hourlyRate: parseFloat(formData.hourlyRate),
        total: parseFloat(formData.total),
        date: formData.date.toISOString()
      };

      let savedTimeEntry;
      if (timeEntry) {
        savedTimeEntry = await timeEntryService.updateTimeEntry(timeEntry._id, submitData);
      } else {
        savedTimeEntry = await timeEntryService.createTimeEntry(submitData);
      }

      onSave(savedTimeEntry);
    } catch (error) {
      console.error('Error saving time entry:', error);
    } finally {
      setLoading(false);
    }
  };

  const categories = [
    'general',
    'research',
    'drafting',
    'communication',
    'court',
    'meeting',
    'travel',
    'admin',
    'other'
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box component="form" onSubmit={handleSubmit}>
        <DialogTitle>
          {timeEntry ? 'Edit Time Entry' : 'Add Time Entry'}
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label="Date & Time"
                value={formData.date}
                onChange={handleDateChange}
                renderInput={(params) => (
                  <TextField {...params} fullWidth required />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hours"
                type="number"
                inputProps={{ min: 0, step: 0.25 }}
                value={formData.timeSpent}
                onChange={handleChange('timeSpent')}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                options={cases}
                getOptionLabel={(option) => `${option.caseNumber} - ${option.caseName}`}
                value={cases.find(c => c._id === formData.caseId) || null}
                onChange={handleCaseChange}
                renderInput={(params) => (
                  <TextField {...params} label="Case" required />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={handleChange('description')}
                placeholder="Describe the work performed..."
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Hourly Rate"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.hourlyRate}
                onChange={handleChange('hourlyRate')}
                InputProps={{
                  startAdornment: '$'
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Total Amount"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.total}
                onChange={handleChange('total')}
                InputProps={{
                  startAdornment: '$'
                }}
                helperText="Calculated automatically"
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Activity</InputLabel>
                <Select
                  value={formData.activity}
                  onChange={handleChange('activity')}
                  label="Activity"
                >
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.nonbillable}
                    onChange={handleChange('nonbillable')}
                  />
                }
                label="Non-Billable"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                  control={
                    <Switch
                      checked={formData.billed}
                      onChange={handleChange('billed')}
                      disabled={formData.nonbillable}
                    />
                  }
                  label="Already Billed"
                />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : (timeEntry ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Box>
    </LocalizationProvider>
  );
};

export default TimeEntryForm;