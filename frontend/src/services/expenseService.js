import api from './api';

const expenseService = {
  async getExpenses(params = {}) {
    const response = await api.get('/expenses', { params });
    return response.data;
  },

  async getExpense(id) {
    const response = await api.get(`/expenses/${id}`);
    return response.data;
  },

  async createExpense(expenseData) {
    const response = await api.post('/expenses', expenseData);
    return response.data;
  },

  async updateExpense(id, expenseData) {
    const response = await api.put(`/expenses/${id}`, expenseData);
    return response.data;
  },

  async deleteExpense(id) {
    const response = await api.delete(`/expenses/${id}`);
    return response.data;
  },

  async createBulkExpenses(expenses) {
    const response = await api.post('/expenses/bulk', { expenses });
    return response.data;
  },

  async getExpenseSummary(params = {}) {
    const response = await api.get('/expenses/reports/summary', { params });
    return response.data;
  }
};

export default expenseService;