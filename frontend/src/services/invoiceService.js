import api from './api';

const invoiceService = {
  async getInvoices(params = {}) {
    const response = await api.get('/invoices', { params });
    return response.data;
  },

  async getInvoice(id) {
    const response = await api.get(`/invoices/${id}`);
    return response.data;
  },

  async createInvoice(invoiceData) {
    const response = await api.post('/invoices', invoiceData);
    return response.data;
  },

  async updateInvoice(id, invoiceData) {
    const response = await api.put(`/invoices/${id}`, invoiceData);
    return response.data;
  },

  async deleteInvoice(id) {
    const response = await api.delete(`/invoices/${id}`);
    return response.data;
  },

  async updateInvoiceStatus(id, status, additionalData = {}) {
    const response = await api.put(`/invoices/${id}/status`, { 
      status, 
      ...additionalData 
    });
    return response.data;
  },

  async generateInvoicePDF(id) {
    const response = await api.get(`/invoices/${id}/pdf`);
    return response.data;
  },

  async getInvoiceSummary(params = {}) {
    const response = await api.get('/invoices/reports/summary', { params });
    return response.data;
  }
};

export default invoiceService;