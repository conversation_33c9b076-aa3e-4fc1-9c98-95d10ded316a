import api from './api';

const contactService = {
  // Get all contacts with pagination and filtering
  getContacts: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/contacts?${queryString}`);
    return response.data;
  },

  // Get single contact by ID
  getContact: async (id) => {
    const response = await api.get(`/contacts/${id}`);
    return response.data;
  },

  // Create new contact
  createContact: async (contactData) => {
    const response = await api.post('/contacts', contactData);
    return response.data;
  },

  // Update contact
  updateContact: async (id, contactData) => {
    const response = await api.put(`/contacts/${id}`, contactData);
    return response.data;
  },

  // Archive contact (soft delete)
  archiveContact: async (id) => {
    const response = await api.delete(`/contacts/${id}`);
    return response.data;
  },

  // Restore archived contact
  restoreContact: async (id) => {
    const response = await api.post(`/contacts/${id}/restore`);
    return response.data;
  },

  // Quick search for contact picker/autocomplete
  quickSearch: async (query, limit = 10) => {
    const response = await api.get(`/contacts/search/quick?q=${encodeURIComponent(query)}&limit=${limit}`);
    return response.data;
  }
};

export default contactService;