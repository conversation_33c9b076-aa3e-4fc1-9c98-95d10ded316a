import api from './api';

const documentService = {
  async getDocuments(params = {}) {
    const response = await api.get('/documents', { params });
    return response.data;
  },

  async getDocument(id) {
    const response = await api.get(`/documents/${id}`);
    return response.data;
  },

  async createDocument(documentData) {
    const response = await api.post('/documents', documentData);
    return response.data;
  },

  async updateDocument(id, documentData) {
    const response = await api.put(`/documents/${id}`, documentData);
    return response.data;
  },

  async deleteDocument(id) {
    const response = await api.delete(`/documents/${id}`);
    return response.data;
  },

  async uploadDocument(formData) {
    const response = await api.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async downloadDocument(id) {
    const response = await api.get(`/documents/${id}/download`, {
      responseType: 'blob',
    });
    return response;
  },

  async searchDocuments(query) {
    const response = await api.get('/documents/search', { params: query });
    return response.data;
  }
};

export default documentService;