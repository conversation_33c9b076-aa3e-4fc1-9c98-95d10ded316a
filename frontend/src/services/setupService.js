import api from './api';

const setupService = {
  async resetDatabase() {
    try {
      const response = await api.post('/setup/reset-database');
      return response.data;
    } catch (error) {
      console.error('Error in resetDatabase:', error);
      throw error;
    }
  },

  async createAdmin() {
    try {
      const response = await api.post('/setup/create-admin');
      return response.data;
    } catch (error) {
      console.error('Error in createAdmin:', error);
      throw error;
    }
  },

  async testEndpoint() {
    try {
      const response = await api.post('/setup/test');
      return response.data;
    } catch (error) {
      console.error('Error in testEndpoint:', error);
      throw error;
    }
  }
};

export default setupService;