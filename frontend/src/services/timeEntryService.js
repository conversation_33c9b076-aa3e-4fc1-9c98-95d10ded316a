import api from './api';

const timeEntryService = {
  // Get all time entries with pagination and filtering
  getTimeEntries: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/time-entries?${queryString}`);
    return response.data;
  },

  // Get single time entry by ID
  getTimeEntry: async (id) => {
    const response = await api.get(`/time-entries/${id}`);
    return response.data;
  },

  // Create new time entry
  createTimeEntry: async (timeEntryData) => {
    const response = await api.post('/time-entries', timeEntryData);
    return response.data;
  },

  // Update time entry
  updateTimeEntry: async (id, timeEntryData) => {
    const response = await api.put(`/time-entries/${id}`, timeEntryData);
    return response.data;
  },

  // Delete time entry
  deleteTimeEntry: async (id) => {
    const response = await api.delete(`/time-entries/${id}`);
    return response.data;
  },

  // Create multiple time entries at once
  createBulkTimeEntries: async (entries) => {
    const response = await api.post('/time-entries/bulk', { entries });
    return response.data;
  },

  // Get time entry summary report
  getTimeEntrySummary: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/time-entries/reports/summary?${queryString}`);
    return response.data;
  }
};

export default timeEntryService;