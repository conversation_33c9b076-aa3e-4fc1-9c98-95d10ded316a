import api from './api';

export const leadService = {
  async getLeads(params = {}) {
    try {
      const response = await api.get('/leads', { params });
      return response.data;
    } catch (error) {
      console.error('Error in getLeads:', error);
      throw error;
    }
  },

  async getLead(id) {
    try {
      const response = await api.get(`/leads/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in getLead:', error);
      throw error;
    }
  },

  async createLead(leadData) {
    try {
      const response = await api.post('/leads', leadData);
      return response.data;
    } catch (error) {
      console.error('Error in createLead:', error);
      throw error;
    }
  },

  async updateLead(id, leadData) {
    try {
      const response = await api.put(`/leads/${id}`, leadData);
      return response.data;
    } catch (error) {
      console.error('Error in updateLead:', error);
      throw error;
    }
  },

  async deleteLead(id) {
    try {
      const response = await api.delete(`/leads/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in deleteLead:', error);
      throw error;
    }
  },

  async updateLeadStatus(id, status) {
    try {
      const response = await api.put(`/leads/${id}/status`, { status });
      return response.data;
    } catch (error) {
      console.error('Error in updateLeadStatus:', error);
      throw error;
    }
  },

  async getLeadSummary(params = {}) {
    try {
      const response = await api.get('/leads/reports/summary', { params });
      return response.data;
    } catch (error) {
      console.error('Error in getLeadSummary:', error);
      throw error;
    }
  },

  async getStaffLeads() {
    try {
      console.log('Fetching staff leads...');
      const response = await api.get('/leads/staff');
      console.log('Staff leads response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in getStaffLeads:', error);
      console.error('Error response:', error.response);
      throw error;
    }
  },

  async updateLeadPriority(id, priority) {
    try {
      const response = await api.put(`/leads/${id}/priority`, { priority });
      return response.data;
    } catch (error) {
      console.error('Error in updateLeadPriority:', error);
      throw error;
    }
  }
};