import api from './api';

const noteService = {
  getNotes: async (params = {}) => {
    try {
      const response = await api.get('/notes', { params });
      return response.data;
    } catch (error) {
      console.error('Error in getNotes:', error);
      throw error;
    }
  },

  createNote: async (noteData) => {
    try {
      const response = await api.post('/notes', noteData);
      return response.data;
    } catch (error) {
      console.error('Error in createNote:', error);
      throw error;
    }
  },

  updateNote: async (id, noteData) => {
    try {
      const response = await api.put(`/notes/${id}`, noteData);
      return response.data;
    } catch (error) {
      console.error('Error in updateNote:', error);
      throw error;
    }
  },

  deleteNote: async (id) => {
    try {
      const response = await api.delete(`/notes/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in deleteNote:', error);
      throw error;
    }
  }
};

export default noteService;