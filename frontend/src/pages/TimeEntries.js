import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  Menu,
  MenuItem,
  TablePagination,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  Card,
  CardContent,
  Grid,

} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Timer as TimerIcon,
  AttachMoney as MoneyIcon,

  FilterList as FilterIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import timeEntryService from '../services/timeEntryService';
import caseService from '../services/caseService';
import TimeEntryForm from '../components/TimeEntryForm';

const TimeEntries = () => {
  const [timeEntries, setTimeEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [caseFilter, setCaseFilter] = useState('');
  const [billableFilter, setBillableFilter] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalTimeEntries, setTotalTimeEntries] = useState(0);
  const [selectedTimeEntry, setSelectedTimeEntry] = useState(null);
  const [showTimeEntryForm, setShowTimeEntryForm] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [summary, setSummary] = useState({
    totalHours: 0,
    totalAmount: 0,
    billableHours: 0,
    billableAmount: 0
  });
  const [cases, setCases] = useState([]);

  const fetchTimeEntries = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
        caseId: caseFilter,
        billable: billableFilter,
        startDate: startDate ? startDate.toISOString().split('T')[0] : '',
        endDate: endDate ? endDate.toISOString().split('T')[0] : ''
      };

      // Remove empty params
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null) {
          delete params[key];
        }
      });

      const data = await timeEntryService.getTimeEntries(params);
      setTimeEntries(data.timeEntries);
      setTotalTimeEntries(data.pagination.total);
      setSummary(data.summary);
    } catch (error) {
      console.error('Error fetching time entries:', error);
      showSnackbar('Error fetching time entries', 'error');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchTerm, caseFilter, billableFilter, startDate, endDate]);

  useEffect(() => {
    fetchTimeEntries();
  }, [fetchTimeEntries]);

  useEffect(() => {
    const fetchCases = async () => {
      try {
        const response = await caseService.getCases({ limit: 1000 });
        setCases(response.cases);
      } catch (error) {
        console.error('Error fetching cases:', error);
      }
    };
    fetchCases();
  }, []);

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleCreateTimeEntry = () => {
    setSelectedTimeEntry(null);
    setShowTimeEntryForm(true);
  };

  const handleEditTimeEntry = (timeEntry) => {
    setSelectedTimeEntry(timeEntry);
    setShowTimeEntryForm(true);
    setAnchorEl(null);
  };

  const handleDeleteTimeEntry = async (timeEntry) => {
    if (window.confirm(`Are you sure you want to delete this time entry?`)) {
      try {
        await timeEntryService.deleteTimeEntry(timeEntry._id);
        showSnackbar('Time entry deleted successfully');
        fetchTimeEntries();
      } catch (error) {
        console.error('Error deleting time entry:', error);
        showSnackbar('Error deleting time entry', 'error');
      }
    }
    setAnchorEl(null);
  };

  const handleTimeEntrySaved = (savedTimeEntry) => {
    setShowTimeEntryForm(false);
    setSelectedTimeEntry(null);
    fetchTimeEntries();
    showSnackbar(
      selectedTimeEntry 
        ? 'Time entry updated successfully'
        : 'Time entry created successfully'
    );
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuClick = (event, timeEntry) => {
    setAnchorEl(event.currentTarget);
    setSelectedTimeEntry(timeEntry);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTimeEntry(null);
  };

  const formatHours = (hours) => {
    return hours ? hours.toFixed(2) : '0.00';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Time Entries
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTimeEntry}
          >
            Add Time Entry
          </Button>
        </Box>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TimerIcon color="primary" sx={{ mr: 1 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Hours
                    </Typography>
                    <Typography variant="h6">
                      {formatHours(summary.totalHours)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <MoneyIcon color="primary" sx={{ mr: 1 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Amount
                    </Typography>
                    <Typography variant="h6">
                      {formatCurrency(summary.totalAmount)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TimerIcon color="success" sx={{ mr: 1 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Billable Hours
                    </Typography>
                    <Typography variant="h6">
                      {formatHours(summary.totalHours)} {/* Using totalHours as placeholder */}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <MoneyIcon color="success" sx={{ mr: 1 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Billable Amount
                    </Typography>
                    <Typography variant="h6">
                      {formatCurrency(summary.totalAmount)} {/* Using totalAmount as placeholder */}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search time entries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Case</InputLabel>
                <Select
                  value={caseFilter}
                  onChange={(e) => setCaseFilter(e.target.value)}
                  label="Case"
                >
                  <MenuItem value="">All Cases</MenuItem>
                  {cases.map((caseItem) => (
                    <MenuItem key={caseItem._id} value={caseItem._id}>
                      {caseItem.caseNumber} - {caseItem.caseName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Billable</InputLabel>
                <Select
                  value={billableFilter}
                  onChange={(e) => setBillableFilter(e.target.value)}
                  label="Billable"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">Billable</MenuItem>
                  <MenuItem value="false">Non-Billable</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={setStartDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            
            <Grid item xs={12} md={2}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={setEndDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => {
                  setSearchTerm('');
                  setCaseFilter('');
                  setBillableFilter('');
                  setStartDate(null);
                  setEndDate(null);
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Time Entries Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Case</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Description</TableCell>
                <TableCell align="right">Hours</TableCell>
                <TableCell align="right">Rate</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell>Billable</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : timeEntries.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    No time entries found
                  </TableCell>
                </TableRow>
              ) : (
                timeEntries.map((timeEntry) => (
                  <TableRow 
                    key={timeEntry._id}
                    sx={{ '&:hover': { bgcolor: 'action.hover' } }}
                  >
                    <TableCell>
                      {formatDate(timeEntry.date)}
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2">
                          {timeEntry.case?.caseNumber || 'N/A'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {timeEntry.case?.caseName}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {timeEntry.user ? 
                        `${timeEntry.user.firstName} ${timeEntry.user.lastName}` : 
                        'Unknown User'
                      }
                    </TableCell>
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          maxWidth: 200, 
                          overflow: 'hidden', 
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {timeEntry.description}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      {formatHours(timeEntry.hours)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(timeEntry.rate)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(timeEntry.amount)}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={timeEntry.billable ? 'Billable' : 'Non-Billable'} 
                        size="small"
                        color={timeEntry.billable ? 'success' : 'default'}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        onClick={(e) => handleMenuClick(e, timeEntry)}
                        size="small"
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={totalTimeEntries}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </TableContainer>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => handleEditTimeEntry(selectedTimeEntry)}>
            <EditIcon sx={{ mr: 1 }} />
            Edit
          </MenuItem>
          <MenuItem onClick={() => handleDeleteTimeEntry(selectedTimeEntry)}>
            <DeleteIcon sx={{ mr: 1 }} />
            Delete
          </MenuItem>
        </Menu>

        {/* Time Entry Form Dialog */}
        <Dialog
          open={showTimeEntryForm}
          onClose={() => setShowTimeEntryForm(false)}
          maxWidth="md"
          fullWidth
        >
          <TimeEntryForm
            timeEntry={selectedTimeEntry}
            onSave={handleTimeEntrySaved}
            onCancel={() => setShowTimeEntryForm(false)}
          />
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
        >
          <Alert 
            onClose={handleCloseSnackbar} 
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default TimeEntries;