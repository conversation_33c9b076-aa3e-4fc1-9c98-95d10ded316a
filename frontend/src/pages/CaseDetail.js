import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  CalendarToday,
  Note,
  Description,
  Payment,
  Edit,
  Add,
  Delete,
  Visibility,
  Download,
  AttachMoney,
  Person,
  Business,
  Phone,
  Email,
  LocationOn
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { caseService } from '../services/caseService';
import documentService from '../services/documentService';
import billingService from '../services/billingService';

const CaseDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [caseData, setCaseData] = useState(null);
  const [documents, setDocuments] = useState([]);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  
  // Dialog states
  const [noteDialog, setNoteDialog] = useState(false);
  const [dateDialog, setDateDialog] = useState(false);
  const [newNote, setNewNote] = useState('');
  const [newDate, setNewDate] = useState({
    title: '',
    date: '',
    type: 'court_date',
    description: ''
  });

  const dateTypes = [
    { value: 'court_date', label: 'Court Date' },
    { value: 'deadline', label: 'Deadline' },
    { value: 'meeting', label: 'Meeting' },
    { value: 'filing_deadline', label: 'Filing Deadline' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    loadCaseData();
  }, [id]);

  const loadCaseData = async () => {
    try {
      setLoading(true);
      const [caseResponse, documentsResponse, paymentsResponse] = await Promise.all([
        caseService.getCase(id),
        documentService.getDocuments({ caseId: id, limit: 50 }),
        billingService.getPayments({ caseId: id, limit: 50 })
      ]);
      
      setCaseData(caseResponse);
      setDocuments(documentsResponse.documents);
      setPayments(paymentsResponse.payments);
    } catch (error) {
      console.error('Failed to load case data:', error);
      showSnackbar('Failed to load case data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleAddNote = async () => {
    try {
      await caseService.addNote(id, { content: newNote });
      showSnackbar('Note added successfully');
      setNewNote('');
      setNoteDialog(false);
      loadCaseData();
    } catch (error) {
      console.error('Failed to add note:', error);
      showSnackbar('Failed to add note', 'error');
    }
  };

  const handleAddDate = async () => {
    try {
      await caseService.addImportantDate(id, newDate);
      showSnackbar('Important date added successfully');
      setNewDate({
        title: '',
        date: '',
        type: 'court_date',
        description: ''
      });
      setDateDialog(false);
      loadCaseData();
    } catch (error) {
      console.error('Failed to add important date:', error);
      showSnackbar('Failed to add important date', 'error');
    }
  };

  const handleDeleteNote = async (noteId) => {
    if (window.confirm('Are you sure you want to delete this note?')) {
      try {
        await caseService.deleteNote(id, noteId);
        showSnackbar('Note deleted successfully');
        loadCaseData();
      } catch (error) {
        console.error('Failed to delete note:', error);
        showSnackbar('Failed to delete note', 'error');
      }
    }
  };

  const handleDeleteDate = async (dateId) => {
    if (window.confirm('Are you sure you want to delete this important date?')) {
      try {
        await caseService.deleteImportantDate(id, dateId);
        showSnackbar('Important date deleted successfully');
        loadCaseData();
      } catch (error) {
        console.error('Failed to delete important date:', error);
        showSnackbar('Failed to delete important date', 'error');
      }
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'success';
      case 'closed': return 'default';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'court_date': return 'error';
      case 'deadline': return 'warning';
      case 'meeting': return 'info';
      case 'filing_deadline': return 'secondary';
      default: return 'default';
    }
  };

  const getTypeLabel = (type) => {
    const typeObj = dateTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading case details...</Typography>
      </Box>
    );
  }

  if (!caseData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Case not found</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            {caseData.caseName}
          </Typography>
          <Typography variant="h6" color="textSecondary">
            Case #{caseData.caseNumber}
          </Typography>
        </Box>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            onClick={() => navigate(`/cases/edit/${id}`)}
            startIcon={<Edit />}
          >
            Edit Case
          </Button>
          <Button
            variant="contained"
            onClick={() => navigate('/cases')}
          >
            Back to Cases
          </Button>
        </Box>
      </Box>

      {/* Basic Information */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Case Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Person sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography><strong>Client:</strong> {caseData.clientName}</Typography>
                </Box>
                <Box display="flex" alignItems="center" mb={2}>
                  <Email sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography><strong>Email:</strong> {caseData.clientEmail || 'N/A'}</Typography>
                </Box>
                <Box display="flex" alignItems="center" mb={2}>
                  <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography><strong>Phone:</strong> {caseData.clientPhone || 'N/A'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Business sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography><strong>Case Type:</strong> {caseData.caseType}</Typography>
                </Box>
                <Box display="flex" alignItems="center" mb={2}>
                  <Chip
                    label={caseData.status}
                    color={getStatusColor(caseData.status)}
                    size="small"
                  />
                </Box>
                <Box display="flex" alignItems="center" mb={2}>
                  <CalendarToday sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography><strong>Open Date:</strong> {formatDate(caseData.openDate)}</Typography>
                </Box>
              </Grid>
            </Grid>
            {caseData.description && (
              <Box mt={2}>
                <Typography variant="subtitle2" gutterBottom>Description:</Typography>
                <Typography>{caseData.description}</Typography>
              </Box>
            )}
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Financial Summary
            </Typography>
            <Box display="flex" alignItems="center" mb={2}>
              <AttachMoney sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography><strong>Total Billed:</strong> {formatCurrency(caseData.totalBilled || 0)}</Typography>
            </Box>
            <Box display="flex" alignItems="center" mb={2}>
              <AttachMoney sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography><strong>Total Paid:</strong> {formatCurrency(caseData.totalPaid || 0)}</Typography>
            </Box>
            <Box display="flex" alignItems="center" mb={2}>
              <AttachMoney sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography><strong>Balance:</strong> {formatCurrency((caseData.totalBilled || 0) - (caseData.totalPaid || 0))}</Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Important Dates" icon={<CalendarToday />} />
          <Tab label="Notes" icon={<Note />} />
          <Tab label="Documents" icon={<Description />} />
          <Tab label="Payments" icon={<Payment />} />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Paper sx={{ p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">Important Dates</Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setDateDialog(true)}
            >
              Add Date
            </Button>
          </Box>
          {caseData.importantDates && caseData.importantDates.length > 0 ? (
            <List>
              {caseData.importantDates.map((date, index) => (
                <ListItem key={index} divider>
                  <ListItemIcon>
                    <CalendarToday color={getTypeColor(date.type)} />
                  </ListItemIcon>
                  <ListItemText
                    primary={date.title}
                    secondary={
                      <Box>
                        <Typography variant="body2">
                          {formatDate(date.date)} - {getTypeLabel(date.type)}
                        </Typography>
                        {date.description && (
                          <Typography variant="body2" color="textSecondary">
                            {date.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleDeleteDate(date._id)}
                  >
                    <Delete />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography color="textSecondary">No important dates</Typography>
          )}
        </Paper>
      )}

      {activeTab === 1 && (
        <Paper sx={{ p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">Notes</Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setNoteDialog(true)}
            >
              Add Note
            </Button>
          </Box>
          {caseData.notes && caseData.notes.length > 0 ? (
            <List>
              {caseData.notes.map((note, index) => (
                <ListItem key={index} divider>
                  <ListItemIcon>
                    <Note />
                  </ListItemIcon>
                  <ListItemText
                    primary={note.content}
                    secondary={
                      <Typography variant="caption">
                        By: {note.createdBy?.firstName} {note.createdBy?.lastName} on {formatDate(note.createdAt)}
                      </Typography>
                    }
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleDeleteNote(note._id)}
                  >
                    <Delete />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography color="textSecondary">No notes</Typography>
          )}
        </Paper>
      )}

      {activeTab === 2 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Documents</Typography>
          {documents.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Size</TableCell>
                    <TableCell>Uploaded</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {documents.map((doc) => (
                    <TableRow key={doc._id}>
                      <TableCell>{doc.name}</TableCell>
                      <TableCell>{doc.type}</TableCell>
                      <TableCell>{doc.fileSize ? `${(doc.fileSize / 1024).toFixed(1)} KB` : 'N/A'}</TableCell>
                      <TableCell>{formatDate(doc.uploadDate)}</TableCell>
                      <TableCell>
                        <IconButton size="small">
                          <Visibility />
                        </IconButton>
                        <IconButton size="small">
                          <Download />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography color="textSecondary">No documents</Typography>
          )}
        </Paper>
      )}

      {activeTab === 3 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Payments</Typography>
          {payments.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Method</TableCell>
                    <TableCell>Paid By</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment._id}>
                      <TableCell>{formatDate(payment.createdAt)}</TableCell>
                      <TableCell>{formatCurrency(payment.amount)}</TableCell>
                      <TableCell>{payment.paymentMethod}</TableCell>
                      <TableCell>{payment.paidBy}</TableCell>
                      <TableCell>
                        <Chip
                          label={payment.status}
                          color={payment.status === 'completed' ? 'success' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography color="textSecondary">No payments</Typography>
          )}
        </Paper>
      )}

      {/* Add Note Dialog */}
      <Dialog open={noteDialog} onClose={() => setNoteDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Note</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Note Content"
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNoteDialog(false)}>Cancel</Button>
          <Button onClick={handleAddNote} variant="contained" disabled={!newNote.trim()}>
            Add Note
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Important Date Dialog */}
      <Dialog open={dateDialog} onClose={() => setDateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Important Date</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Title"
                value={newDate.title}
                onChange={(e) => setNewDate({ ...newDate, title: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="date"
                label="Date"
                value={newDate.date}
                onChange={(e) => setNewDate({ ...newDate, date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={newDate.type}
                  onChange={(e) => setNewDate({ ...newDate, type: e.target.value })}
                  label="Type"
                >
                  {dateTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description (Optional)"
                value={newDate.description}
                onChange={(e) => setNewDate({ ...newDate, description: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDateDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleAddDate} 
            variant="contained" 
            disabled={!newDate.title || !newDate.date}
          >
            Add Date
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CaseDetail;