import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  Grid,
  IconButton,
  Alert,
  Snackbar
} from '@mui/material';
import { Add, Refresh, Search, Clear, Edit, Delete, Visibility } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { caseService } from '../services/caseService';
import setupService from '../services/setupService';
import { useAuth } from '../contexts/AuthContext';

const Cases = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [editingCase, setEditingCase] = useState(null);
  const [viewingCase, setViewingCase] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [resetting, setResetting] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [formData, setFormData] = useState({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    caseType: '',
    description: '',
    clientAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    }
  });

  const caseTypes = [
    { value: 'personal_injury', label: 'Personal Injury' },
    { value: 'criminal_defense', label: 'Criminal Defense' },
    { value: 'family_law', label: 'Family Law' },
    { value: 'family_law_tx', label: 'Family Law TX' },
    { value: 'family_law_il', label: 'Family Law IL' },
    { value: 'civil_litigation', label: 'Civil Litigation' },
    { value: 'business_law', label: 'Business Law' },
    { value: 'estate_planning', label: 'Estate Planning' },
    { value: 'immigration', label: 'Immigration' },
    { value: 'employment_law', label: 'Employment Law' },
    { value: 'real_estate', label: 'Real Estate' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    loadCases();
  }, [page, searchTerm]);

  const loadCases = async () => {
    try {
      const data = await caseService.getCases({ 
        page, 
        limit: 10,
        search: searchTerm
      });
      setCases(data.cases);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('Failed to load cases:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPage(1);
    loadCases();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingCase) {
        await caseService.updateCase(editingCase._id, formData);
        setSnackbar({ open: true, message: 'Case updated successfully!', severity: 'success' });
      } else {
        await caseService.createCase(formData);
        setSnackbar({ open: true, message: 'Case created successfully!', severity: 'success' });
      }
      setOpen(false);
      setEditingCase(null);
      resetForm();
      loadCases();
    } catch (error) {
      console.error('Failed to save case:', error);
      setSnackbar({ open: true, message: error.response?.data?.message || 'Failed to save case', severity: 'error' });
    }
  };

  const resetForm = () => {
    setFormData({
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      caseType: '',
      description: '',
      clientAddress: {
        street: '',
        city: '',
        state: '',
        zipCode: ''
      }
    });
  };

  const handleEdit = (caseData) => {
    setEditingCase(caseData);
    setFormData({
      clientName: caseData.clientName || '',
      clientEmail: caseData.clientEmail || '',
      clientPhone: caseData.clientPhone || '',
      caseType: caseData.caseType || '',
      description: caseData.description || '',
      clientAddress: {
        street: caseData.clientAddress?.street || '',
        city: caseData.clientAddress?.city || '',
        state: caseData.clientAddress?.state || '',
        zipCode: caseData.clientAddress?.zipCode || ''
      }
    });
    setOpen(true);
  };

  const handleView = async (caseId) => {
    try {
      const caseData = await caseService.getCase(caseId);
      setViewingCase(caseData);
      setShowDetails(true);
    } catch (error) {
      console.error('Failed to load case details:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load case details',
        severity: 'error'
      });
    }
  };

  const handleDelete = async (caseId) => {
    if (!window.confirm('Are you sure you want to delete this case? This action cannot be undone.')) {
      return;
    }
    
    try {
      await caseService.deleteCase(caseId);
      setSnackbar({ open: true, message: 'Case deleted successfully!', severity: 'success' });
      loadCases();
    } catch (error) {
      console.error('Failed to delete case:', error);
      setSnackbar({ open: true, message: error.response?.data?.message || 'Failed to delete case', severity: 'error' });
    }
  };

  const handleResetDatabase = async () => {
    if (!window.confirm('This will wipe all existing data and create sample data. Are you sure?')) {
      return;
    }
    
    setResetting(true);
    try {
      await setupService.resetDatabase(100);
      alert('Database reset successfully! 100 sample cases have been created.');
      loadCases();
    } catch (error) {
      console.error('Failed to reset database:', error);
      alert('Failed to reset database. Please check the console for errors.');
    } finally {
      setResetting(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'success',
      closed: 'default',
      on_hold: 'warning',
      pending: 'info'
    };
    return colors[status] || 'default';
  };

  if (loading) {
    return <Typography>Loading cases...</Typography>;
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Case Management</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {user?.role === 'admin' && (
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleResetDatabase}
              disabled={resetting}
              color="warning"
            >
              {resetting ? 'Resetting...' : 'Reset DB & Create Sample Data'}
            </Button>
          )}
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => {
              setEditingCase(null);
              resetForm();
              setOpen(true);
            }}
          >
            Add New Case
          </Button>
        </Box>
      </Box>

      {/* Advanced Search Bar */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="Search cases by case name, client name, phone, or alien number..."
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              InputProps={{
                endAdornment: (
                  <IconButton onClick={handleSearch}>
                    <Search />
                  </IconButton>
                )
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Search />}
              onClick={handleSearch}
            >
              Search
            </Button>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Clear />}
              onClick={() => {
                setSearchTerm('');
                setPage(1);
              }}
            >
              Clear
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Case Number</TableCell>
              <TableCell>Client Name</TableCell>
              <TableCell>Case Type</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Attorney</TableCell>
              <TableCell>Total Billed</TableCell>
              <TableCell>Total Paid</TableCell>
              <TableCell>Balance</TableCell>
              <TableCell>Expenses</TableCell>
              <TableCell>Created Date</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cases.map((caseData) => (
              <TableRow
                key={caseData._id}
                sx={{ '&:hover': { backgroundColor: 'grey.50' } }}
              >
                <TableCell>{caseData.caseNumber}</TableCell>
                <TableCell>{caseData.clientName}</TableCell>
                <TableCell>
                  {caseTypes.find(type => type.value === caseData.caseType)?.label || caseData.caseType}
                </TableCell>
                <TableCell>
                  <Chip
                    label={caseData.status}
                    color={getStatusColor(caseData.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {caseData.attorney ? `${caseData.attorney.firstName} ${caseData.attorney.lastName}` : 'Unassigned'}
                </TableCell>
                <TableCell>${caseData.financials?.totalBilled || caseData.totalBilled || 0}</TableCell>
                <TableCell>${caseData.financials?.totalPaid || caseData.totalPaid || 0}</TableCell>
                <TableCell>${caseData.financials?.remainingBalance || (caseData.totalBilled || 0) - (caseData.totalPaid || 0)}</TableCell>
                <TableCell>${caseData.financials?.expenses || 0}</TableCell>
                <TableCell>
                  {new Date(caseData.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                                              onClick={(e) => {
                          e.stopPropagation();
                          handleView(caseData._id);
                        }}
                      title="View Details"
                    >
                      <Search />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(caseData);
                      }}
                      title="Edit Case"
                      color="primary"
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(caseData._id);
                      }}
                      title="Delete Case"
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={(e, value) => setPage(value)}
          />
        </Box>
      )}

      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md" fullWidth>
        <form onSubmit={handleSubmit}>
          <DialogTitle>{editingCase ? 'Edit Case' : 'Add New Case'}</DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mt: 1 }}>
              <TextField
                label="Client Name"
                fullWidth
                variant="outlined"
                required
                value={formData.clientName}
                onChange={(e) => setFormData({ ...formData, clientName: e.target.value })}
              />
              <TextField
                label="Client Email"
                type="email"
                fullWidth
                variant="outlined"
                value={formData.clientEmail}
                onChange={(e) => setFormData({ ...formData, clientEmail: e.target.value })}
              />
              <TextField
                label="Client Phone"
                fullWidth
                variant="outlined"
                value={formData.clientPhone}
                onChange={(e) => setFormData({ ...formData, clientPhone: e.target.value })}
              />
              <FormControl fullWidth>
                <InputLabel>Case Type</InputLabel>
                <Select
                  value={formData.caseType}
                  label="Case Type"
                  onChange={(e) => setFormData({ ...formData, caseType: e.target.value })}
                  required
                >
                  {caseTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            
            <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>Client Address</Typography>
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
              <TextField
                label="Street Address"
                fullWidth
                variant="outlined"
                value={formData.clientAddress.street}
                onChange={(e) => setFormData({
                  ...formData,
                  clientAddress: { ...formData.clientAddress, street: e.target.value }
                })}
                sx={{ gridColumn: '1 / -1' }}
              />
              <TextField
                label="City"
                fullWidth
                variant="outlined"
                value={formData.clientAddress.city}
                onChange={(e) => setFormData({
                  ...formData,
                  clientAddress: { ...formData.clientAddress, city: e.target.value }
                })}
              />
              <TextField
                label="State"
                fullWidth
                variant="outlined"
                value={formData.clientAddress.state}
                onChange={(e) => setFormData({
                  ...formData,
                  clientAddress: { ...formData.clientAddress, state: e.target.value }
                })}
              />
              <TextField
                label="ZIP Code"
                fullWidth
                variant="outlined"
                value={formData.clientAddress.zipCode}
                onChange={(e) => setFormData({
                  ...formData,
                  clientAddress: { ...formData.clientAddress, zipCode: e.target.value }
                })}
              />
            </Box>
            
            <TextField
              label="Case Description"
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained">
              {editingCase ? 'Update Case' : 'Create Case'}
            </Button>
          </DialogActions>
        </form>
              </Dialog>

        {/* Case Details Dialog */}
        <Dialog
          open={showDetails}
          onClose={() => setShowDetails(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6">Case Details</Typography>
              <IconButton onClick={() => setShowDetails(false)}>
                <Clear />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            {viewingCase && (
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>Basic Information</Typography>
                    <Typography><strong>Case Number:</strong> {viewingCase.caseNumber}</Typography>
                    <Typography><strong>Case Name:</strong> {viewingCase.caseName}</Typography>
                    <Typography><strong>Client Name:</strong> {viewingCase.clientName}</Typography>
                    <Typography><strong>Client Email:</strong> {viewingCase.clientEmail || 'N/A'}</Typography>
                    <Typography><strong>Client Phone:</strong> {viewingCase.clientPhone || 'N/A'}</Typography>
                    <Typography><strong>Case Type:</strong> {viewingCase.caseType}</Typography>
                    <Typography><strong>Status:</strong> {viewingCase.status}</Typography>
                    <Typography><strong>Open Date:</strong> {viewingCase.openDate ? new Date(viewingCase.openDate).toLocaleDateString() : 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>Additional Details</Typography>
                    <Typography><strong>Description:</strong> {viewingCase.description || 'N/A'}</Typography>
                    <Typography><strong>Practice Area:</strong> {viewingCase.practiceArea || 'N/A'}</Typography>
                    <Typography><strong>Referral Source:</strong> {viewingCase.referralSource || 'N/A'}</Typography>
                    <Typography><strong>Lead Attorney:</strong> {viewingCase.leadAttorneyName || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>Important Dates</Typography>
                    {viewingCase.importantDates && viewingCase.importantDates.length > 0 ? (
                      <Box>
                        {viewingCase.importantDates.map((date, index) => (
                          <Box key={index} mb={1} p={1} border={1} borderColor="grey.300" borderRadius={1}>
                            <Typography><strong>{date.title}</strong></Typography>
                            <Typography>Date: {new Date(date.date).toLocaleDateString()}</Typography>
                            {date.description && <Typography>Description: {date.description}</Typography>}
                            <Typography>Type: {date.type}</Typography>
                          </Box>
                        ))}
                      </Box>
                    ) : (
                      <Typography color="textSecondary">No important dates</Typography>
                    )}
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>Notes</Typography>
                    {viewingCase.notes && viewingCase.notes.length > 0 ? (
                      <Box>
                        {viewingCase.notes.map((note, index) => (
                          <Box key={index} mb={1} p={1} border={1} borderColor="grey.300" borderRadius={1}>
                            <Typography>{note.content}</Typography>
                            <Typography variant="caption" color="textSecondary">
                              By: {note.createdBy?.firstName} {note.createdBy?.lastName} on {new Date(note.createdAt).toLocaleDateString()}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    ) : (
                      <Typography color="textSecondary">No notes</Typography>
                    )}
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
        </Dialog>

        {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Cases;