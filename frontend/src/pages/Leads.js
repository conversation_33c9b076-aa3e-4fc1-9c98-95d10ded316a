import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Grid,
  Card,
  CardContent,
  Chip,
  Dialog,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Alert,
  Menu
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  TrendingUp as ConvertIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { leadService } from '../services/leadService';
import LeadForm from '../components/LeadForm';
import LeadsKanban from '../components/LeadsKanban';

const Leads = () => {
  const [leads, setLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [summary, setSummary] = useState(null);
  
  // Staff leads dashboard state
  const [staffLeads, setStaffLeads] = useState([]);
  const [selectedStaff, setSelectedStaff] = useState(null);
  const [showStaffDashboard, setShowStaffDashboard] = useState(true);
  const [viewMode, setViewMode] = useState('kanban'); // 'kanban' or 'table'
  
  // Form dialog state
  const [formOpen, setFormOpen] = useState(false);
  const [editingLead, setEditingLead] = useState(null);
  
  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedLead, setSelectedLead] = useState(null);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedCaseType, setSelectedCaseType] = useState('');
  const [selectedSource, setSelectedSource] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const statuses = ['new', 'contacted', 'qualified', 'converted', 'closed'];
  const caseTypes = ['personal_injury', 'criminal_defense', 'family_law', 'family_law_tx', 'family_law_il', 'civil_litigation', 'business_law', 'estate_planning', 'immigration', 'employment_law', 'real_estate', 'other'];
  const sources = ['website', 'referral', 'advertising', 'social_media', 'google', 'other'];

  useEffect(() => {
    fetchLeads();
    fetchStaffLeads();
  }, [page, rowsPerPage, selectedStatus, selectedCaseType, selectedSource, startDate, endDate]);

  useEffect(() => {
    if (searchTerm) {
      const timeoutId = setTimeout(() => {
        fetchLeads();
      }, 500);
      return () => clearTimeout(timeoutId);
    } else {
      fetchLeads();
    }
  }, [searchTerm]);

  const fetchLeads = async () => {
    if (!showStaffDashboard) {
      setLoading(true);
      setError(null);
      try {
        const params = {
          page: page + 1,
          limit: rowsPerPage,
          status: selectedStatus || undefined,
          caseType: selectedCaseType || undefined,
          source: selectedSource || undefined,
          startDate: startDate ? startDate.toISOString() : undefined,
          endDate: endDate ? endDate.toISOString() : undefined,
          search: searchTerm || undefined,
          assignedTo: selectedStaff || undefined
        };

        const data = await leadService.getLeads(params);
        
        setLeads(data.leads || []);
        setTotalCount(data.pagination?.total || 0);
        setSummary(data.summary || null);
      } catch (error) {
        console.error('Error fetching leads:', error);
        setError('Failed to load leads');
      } finally {
        setLoading(false);
      }
    }
  };

  const fetchStaffLeads = async () => {
    try {
      const data = await leadService.getStaffLeads();
      setStaffLeads(data.staffLeads || []);
    } catch (error) {
      console.error('Error fetching staff leads:', error);
      setError('Failed to load staff leads');
    }
  };

  const handleAdd = () => {
    setEditingLead(null);
    setFormOpen(true);
  };

  const handleEdit = (lead) => {
    setEditingLead(lead);
    setFormOpen(true);
    handleCloseMenu();
  };

  const handleDelete = async (leadId) => {
    if (window.confirm('Are you sure you want to delete this lead?')) {
      try {
        await leadService.deleteLead(leadId);
        fetchLeads();
        handleCloseMenu();
      } catch (error) {
        console.error('Error deleting lead:', error);
        setError('Failed to delete lead');
      }
    }
  };

  const handleStatusChange = async (leadId, newStatus) => {
    try {
      await leadService.updateLeadStatus(leadId, newStatus);
      fetchLeads();
      handleCloseMenu();
    } catch (error) {
      console.error('Error updating lead status:', error);
      setError('Failed to update lead status');
    }
  };

  const handleSaveLead = async (leadData) => {
    try {
      if (editingLead) {
        await leadService.updateLead(editingLead._id, leadData);
      } else {
        await leadService.createLead(leadData);
      }
      setFormOpen(false);
      fetchLeads();
    } catch (error) {
      console.error('Error saving lead:', error);
      setError('Failed to save lead');
    }
  };

  const handleOpenMenu = (event, lead) => {
    setAnchorEl(event.currentTarget);
    setSelectedLead(lead);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedLead(null);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedStatus('');
    setSelectedCaseType('');
    setSelectedSource('');
    setStartDate(null);
    setEndDate(null);
    setSelectedStaff(null);
  };

  const handleStaffClick = (staffId, staffName) => {
    setSelectedStaff(staffId);
    setShowStaffDashboard(false);
    setPage(0);
    fetchLeads();
  };

  const handleBackToDashboard = () => {
    setShowStaffDashboard(true);
    setSelectedStaff(null);
    clearFilters();
  };

  const getStatusChip = (status) => {
    const colors = {
      new: 'info',
      contacted: 'primary',
      qualified: 'warning',
      converted: 'success',
      closed: 'default'
    };
    
    return (
      <Chip 
        label={status?.charAt(0).toUpperCase() + status?.slice(1)} 
        color={colors[status] || 'default'} 
        size="small" 
      />
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return dateString ? new Date(dateString).toLocaleDateString() : 'N/A';
  };

  const formatLabel = (str) => {
    return str?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || '';
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">
            {showStaffDashboard ? 'Leads Dashboard' : `Leads - ${staffLeads.find(s => s.staffId === selectedStaff)?.staffName || 'All'}`}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {!showStaffDashboard && (
              <Button
                variant="outlined"
                onClick={handleBackToDashboard}
              >
                Back to Dashboard
              </Button>
            )}
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
            >
              Add Lead
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Leads Kanban Board */}
        {showStaffDashboard && (
          <LeadsKanban
            onAddLead={handleAdd}
            onEditLead={handleEdit}
            onDeleteLead={handleDelete}
            onStatusChange={handleStatusChange}
          />
        )}

        {/* Summary Cards */}
        {!showStaffDashboard && summary && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total Leads
                  </Typography>
                  <Typography variant="h5">
                    {totalCount}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total Value
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(summary.totalValue)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Conversion Rate
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {summary.conversionRate}%
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Filters */}
        {!showStaffDashboard && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                placeholder="Search leads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  {statuses.map((status) => (
                    <MenuItem key={status} value={status}>
                      {formatLabel(status)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Case Type</InputLabel>
                <Select
                  value={selectedCaseType}
                  onChange={(e) => setSelectedCaseType(e.target.value)}
                  label="Case Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {caseTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {formatLabel(type)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Source</InputLabel>
                <Select
                  value={selectedSource}
                  onChange={(e) => setSelectedSource(e.target.value)}
                  label="Source"
                >
                  <MenuItem value="">All Sources</MenuItem>
                  {sources.map((source) => (
                    <MenuItem key={source} value={source}>
                      {formatLabel(source)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={setStartDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={1}>
              <Button
                variant="outlined"
                onClick={clearFilters}
                startIcon={<FilterIcon />}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </Paper>
        )}

        {/* Leads Table */}
        {!showStaffDashboard && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Contact</TableCell>
                <TableCell>Practice Area</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Source</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Added Date</TableCell>
                <TableCell>Assigned To</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : leads.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    No leads found
                  </TableCell>
                </TableRow>
              ) : (
                leads.map((lead) => (
                  <TableRow key={lead._id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {lead.name}
                        </Typography>
                        {lead.potentialCaseName && (
                          <Typography variant="caption" color="textSecondary">
                            {lead.potentialCaseName}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {lead.contact?.email || 'No email'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {lead.contact?.cellPhone || lead.contact?.workPhone || 'No phone'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={formatLabel(lead.practiceArea || lead.caseType)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>{getStatusChip(lead.status)}</TableCell>
                    <TableCell>{formatLabel(lead.source)}</TableCell>
                    <TableCell>{formatCurrency(lead.value)}</TableCell>
                    <TableCell>{formatDate(lead.addedDate)}</TableCell>
                    <TableCell>
                      {lead.assignedTo ? 
                        `${lead.assignedTo.firstName} ${lead.assignedTo.lastName}` : 
                        'Unassigned'
                      }
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={(e) => handleOpenMenu(e, lead)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={totalCount}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(event, newPage) => setPage(newPage)}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
          />
        </TableContainer>
        )}

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleCloseMenu}
        >
          <MenuItem onClick={() => handleEdit(selectedLead)}>
            <EditIcon sx={{ mr: 1 }} />
            Edit Lead
          </MenuItem>
          {selectedLead?.status !== 'converted' && (
            <MenuItem onClick={() => handleStatusChange(selectedLead._id, 'converted')}>
              <ConvertIcon sx={{ mr: 1 }} />
              Mark as Converted
            </MenuItem>
          )}
          <MenuItem 
            onClick={() => handleDelete(selectedLead?._id)}
            sx={{ color: 'error.main' }}
          >
            <DeleteIcon sx={{ mr: 1 }} />
            Delete Lead
          </MenuItem>
        </Menu>

        {/* Add/Edit Dialog */}
        <Dialog
          open={formOpen}
          onClose={() => setFormOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <LeadForm
            lead={editingLead}
            onSave={handleSaveLead}
            onCancel={() => setFormOpen(false)}
          />
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default Leads;