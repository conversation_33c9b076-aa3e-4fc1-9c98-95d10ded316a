import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Chip,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Note as NoteIcon,
  Assignment as CaseIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

import noteService from '../services/noteService';

function Notes() {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalNotes, setTotalNotes] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [caseFilter, setCaseFilter] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [noteForm, setNoteForm] = useState({
    subject: '',
    content: '',
    caseId: '',
    tags: []
  });

  const fetchNotes = async () => {
    try {
      setLoading(true);
      const response = await noteService.getNotes({
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
        caseId: caseFilter
      });
      setNotes(response.notes);
      setTotalNotes(response.pagination.total);
    } catch (err) {
      setError('Failed to fetch notes');
      console.error('Error fetching notes:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotes();
  }, [page, rowsPerPage, searchTerm, caseFilter]);

  const handleSearch = () => {
    setPage(0);
    fetchNotes();
  };

  const handleCreateNote = () => {
    setEditingNote(null);
    setNoteForm({
      subject: '',
      content: '',
      caseId: '',
      tags: []
    });
    setOpenDialog(true);
  };

  const handleEditNote = (note) => {
    setEditingNote(note);
    setNoteForm({
      subject: note.subject,
      content: note.content,
      caseId: note.caseId?._id || '',
      tags: note.tags || []
    });
    setOpenDialog(true);
  };

  const handleSaveNote = async () => {
    try {
      if (editingNote) {
        await noteService.updateNote(editingNote._id, noteForm);
      } else {
        await noteService.createNote(noteForm);
      }
      setOpenDialog(false);
      fetchNotes();
    } catch (err) {
      setError('Failed to save note');
      console.error('Error saving note:', err);
    }
  };

  const handleDeleteNote = async (noteId) => {
    if (!window.confirm('Are you sure you want to delete this note?')) return;
    
    try {
      await noteService.deleteNote(noteId);
      fetchNotes();
    } catch (err) {
      setError('Failed to delete note');
      console.error('Error deleting note:', err);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return format(new Date(date), 'MMM dd, yyyy h:mm a');
  };

  const truncateContent = (content, maxLength = 100) => {
    if (!content) return 'No content';
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
  };

  if (loading && notes.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography>Loading notes...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ padding: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          <NoteIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Notes Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateNote}
          sx={{ bgcolor: '#1976d2' }}
        >
          Add Note
        </Button>
      </Box>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Notes
              </Typography>
              <Typography variant="h4" component="div">
                {totalNotes.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Search notes..."
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              InputProps={{
                endAdornment: (
                  <IconButton onClick={handleSearch}>
                    <SearchIcon />
                  </IconButton>
                )
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Filter by Case</InputLabel>
              <Select
                value={caseFilter}
                label="Filter by Case"
                onChange={(e) => setCaseFilter(e.target.value)}
              >
                <MenuItem value="">All Cases</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => {
                setSearchTerm('');
                setCaseFilter('');
                setPage(0);
              }}
            >
              Clear
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Notes Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: '#f5f5f5' }}>
              <TableCell><strong>Subject</strong></TableCell>
              <TableCell><strong>Content</strong></TableCell>
              <TableCell><strong>Case</strong></TableCell>
              <TableCell><strong>Created By</strong></TableCell>
              <TableCell><strong>Date</strong></TableCell>
              <TableCell><strong>Actions</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {notes.map((note) => (
              <TableRow key={note._id} hover>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="medium">
                    {note.subject}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Tooltip title={note.content}>
                    <Typography variant="body2" color="textSecondary">
                      {truncateContent(note.content)}
                    </Typography>
                  </Tooltip>
                </TableCell>
                <TableCell>
                  {note.caseId ? (
                    <Box display="flex" alignItems="center">
                      <CaseIcon sx={{ mr: 1, fontSize: 16, color: '#1976d2' }} />
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {note.caseId.caseNumber}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {note.caseId.caseName}
                        </Typography>
                      </Box>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      No case
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {note.createdBy 
                      ? `${note.createdBy.firstName} ${note.createdBy.lastName}`
                      : 'Unknown'
                    }
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(note.noteDate)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleEditNote(note)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteNote(note._id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        <TablePagination
          component="div"
          count={totalNotes}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[10, 25, 50, 100]}
        />
      </TableContainer>

      {/* Add/Edit Note Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingNote ? 'Edit Note' : 'Add New Note'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Subject"
                variant="outlined"
                value={noteForm.subject}
                onChange={(e) => setNoteForm({ ...noteForm, subject: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Content"
                variant="outlined"
                multiline
                rows={6}
                value={noteForm.content}
                onChange={(e) => setNoteForm({ ...noteForm, content: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveNote} variant="contained">
            {editingNote ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Notes;