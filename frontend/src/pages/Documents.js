import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  Grid,
  Card,
  CardContent,
  IconButton,
  Snackbar,
  Alert,
  Tooltip,
  LinearProgress
} from '@mui/material';
import { 
  Add, 
  Delete, 
  Edit, 
  Visibility, 
  Download,
  CloudUpload,
  Search,
  Clear
} from '@mui/icons-material';
import { caseService } from '../services/caseService';
import documentService from '../services/documentService';

const Documents = () => {
  const [documents, setDocuments] = useState([]);
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editingDocument, setEditingDocument] = useState(null);
  const [viewingDocument, setViewingDocument] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCase, setSelectedCase] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'other',
    caseId: ''
  });
  const [file, setFile] = useState(null);

  const documentTypes = [
    { value: 'contract', label: 'Contract' },
    { value: 'court_filing', label: 'Court Filing' },
    { value: 'evidence', label: 'Evidence' },
    { value: 'correspondence', label: 'Correspondence' },
    { value: 'financial', label: 'Financial Document' },
    { value: 'medical', label: 'Medical Record' },
    { value: 'police_report', label: 'Police Report' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    loadDocuments();
    loadCases();
  }, [page, searchTerm, selectedCase, selectedType]);

  const loadDocuments = async () => {
    try {
      const params = { 
        page, 
        limit: 10,
        search: searchTerm,
        caseId: selectedCase,
        type: selectedType
      };
      const data = await documentService.getDocuments(params);
      setDocuments(data.documents);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Failed to load documents:', error);
      showSnackbar('Failed to load documents', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadCases = async () => {
    try {
      const data = await caseService.getCases({ limit: 100 });
      setCases(data.cases);
    } catch (error) {
      console.error('Failed to load cases:', error);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.size > 10 * 1024 * 1024) { // 10MB limit
        showSnackbar('File size must be less than 10MB', 'error');
        return;
      }
      setFile(selectedFile);
      if (!formData.name) {
        setFormData({ ...formData, name: selectedFile.name });
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!file && !editingDocument) {
      showSnackbar('Please select a file to upload', 'error');
      return;
    }

    try {
      setUploading(true);

      if (editingDocument) {
        // Update existing document
        const response = await documentService.updateDocument(editingDocument._id, formData);
        showSnackbar('Document updated successfully');
      } else {
        // Upload new document
        const formDataToSend = new FormData();
        formDataToSend.append('file', file);
        formDataToSend.append('name', formData.name);
        formDataToSend.append('description', formData.description);
        formDataToSend.append('type', formData.type);
        formDataToSend.append('caseId', formData.caseId);

        const response = await documentService.uploadDocument(formDataToSend);
        showSnackbar('Document uploaded successfully');
      }

      handleClose();
      loadDocuments();
    } catch (error) {
      console.error('Document error:', error);
      showSnackbar(error.response?.data?.message || 'Document operation failed', 'error');
    } finally {
      setUploading(false);
    }
  };

  const handleEdit = (document) => {
    setEditingDocument(document);
    setFormData({
      name: document.name,
      description: document.description || '',
      type: document.type,
      caseId: document.caseId._id
    });
    setOpen(true);
  };

  const handleView = (document) => {
    setViewingDocument(document);
    setShowDetails(true);
  };

  const handleDelete = async (document) => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      try {
        await documentService.deleteDocument(document._id);
        showSnackbar('Document deleted successfully');
        loadDocuments();
      } catch (error) {
        console.error('Delete error:', error);
        showSnackbar('Failed to delete document', 'error');
      }
    }
  };

  const handleDownload = async (document) => {
    try {
      const response = await documentService.downloadDocument(document._id);
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = document.fileName || document.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      showSnackbar('Failed to download document', 'error');
    }
  };

  const handleClose = () => {
    setOpen(false);
    setEditingDocument(null);
    setFormData({
      name: '',
      description: '',
      type: 'other',
      caseId: ''
    });
    setFile(null);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getTypeColor = (type) => {
    const colors = {
      contract: 'primary',
      court_filing: 'secondary',
      evidence: 'warning',
      correspondence: 'info',
      financial: 'success',
      medical: 'error',
      police_report: 'default',
      other: 'default'
    };
    return colors[type] || 'default';
  };

  const getTypeLabel = (type) => {
    const typeObj = documentTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading documents...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Documents</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpen(true)}
        >
          Upload Document
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Search Documents"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Case</InputLabel>
              <Select
                value={selectedCase}
                onChange={(e) => setSelectedCase(e.target.value)}
                label="Case"
              >
                <MenuItem value="">All Cases</MenuItem>
                {cases.map((caseItem) => (
                  <MenuItem key={caseItem._id} value={caseItem._id}>
                    {caseItem.caseNumber} - {caseItem.clientName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                label="Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {documentTypes.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              variant="outlined"
              startIcon={<Clear />}
              onClick={() => {
                setSearchTerm('');
                setSelectedCase('');
                setSelectedType('');
              }}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Documents
              </Typography>
              <Typography variant="h4">
                {documents.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Size
              </Typography>
              <Typography variant="h4">
                {formatFileSize(documents.reduce((sum, doc) => sum + (doc.fileSize || 0), 0))}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                This Month
              </Typography>
              <Typography variant="h4">
                {documents.filter(doc => {
                  const uploadDate = new Date(doc.uploadDate);
                  const now = new Date();
                  return uploadDate.getMonth() === now.getMonth() && 
                         uploadDate.getFullYear() === now.getFullYear();
                }).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Documents Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Case</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Size</TableCell>
                <TableCell>Uploaded By</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {documents.map((document) => (
                <TableRow key={document._id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {document.name}
                    </Typography>
                    {document.description && (
                      <Typography variant="caption" color="textSecondary">
                        {document.description}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    {document.caseId?.caseNumber || 'N/A'}
                    <br />
                    <Typography variant="caption" color="textSecondary">
                      {document.caseId?.clientName || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getTypeLabel(document.type)}
                      color={getTypeColor(document.type)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {formatFileSize(document.fileSize || 0)}
                  </TableCell>
                  <TableCell>
                    {document.uploadedBy?.firstName} {document.uploadedBy?.lastName}
                  </TableCell>
                  <TableCell>
                    {new Date(document.uploadDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleView(document)}
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download">
                        <IconButton
                          size="small"
                          onClick={() => handleDownload(document)}
                        >
                          <Download />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Document">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(document)}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Document">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDelete(document)}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Pagination */}
      <Box display="flex" justifyContent="center" mt={3}>
        <Pagination
          count={totalPages}
          page={page}
          onChange={(e, value) => setPage(value)}
          color="primary"
        />
      </Box>

      {/* Upload/Edit Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingDocument ? 'Edit Document' : 'Upload New Document'}
        </DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            {uploading && <LinearProgress sx={{ mb: 2 }} />}
            <Grid container spacing={2}>
              {!editingDocument && (
                <Grid item xs={12}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<CloudUpload />}
                    fullWidth
                    sx={{ py: 2 }}
                  >
                    {file ? file.name : 'Choose File'}
                    <input
                      type="file"
                      hidden
                      onChange={handleFileChange}
                      accept=".pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif"
                    />
                  </Button>
                  {file && (
                    <Typography variant="caption" color="textSecondary" display="block" mt={1}>
                      File size: {formatFileSize(file.size)}
                    </Typography>
                  )}
                </Grid>
              )}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Document Name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Case</InputLabel>
                  <Select
                    value={formData.caseId}
                    onChange={(e) => setFormData({ ...formData, caseId: e.target.value })}
                    label="Case"
                  >
                    {cases.map((caseItem) => (
                      <MenuItem key={caseItem._id} value={caseItem._id}>
                        {caseItem.caseNumber} - {caseItem.clientName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Document Type</InputLabel>
                  <Select
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                    label="Document Type"
                  >
                    {documentTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description (Optional)"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} disabled={uploading}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              variant="contained" 
              disabled={uploading}
              startIcon={editingDocument ? <Edit /> : <CloudUpload />}
            >
              {editingDocument ? 'Update Document' : 'Upload Document'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Document Details Dialog */}
      <Dialog
        open={showDetails}
        onClose={() => setShowDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Document Details</Typography>
            <IconButton onClick={() => setShowDetails(false)}>
              <Visibility />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {viewingDocument && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>Document Information</Typography>
                  <Typography><strong>Name:</strong> {viewingDocument.name}</Typography>
                  <Typography><strong>Type:</strong> {getTypeLabel(viewingDocument.type)}</Typography>
                  <Typography><strong>Size:</strong> {formatFileSize(viewingDocument.fileSize || 0)}</Typography>
                  <Typography><strong>Upload Date:</strong> {new Date(viewingDocument.uploadDate).toLocaleDateString()}</Typography>
                  {viewingDocument.description && (
                    <Typography><strong>Description:</strong> {viewingDocument.description}</Typography>
                  )}
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>Case Information</Typography>
                  <Typography><strong>Case Number:</strong> {viewingDocument.caseId?.caseNumber || 'N/A'}</Typography>
                  <Typography><strong>Case Name:</strong> {viewingDocument.caseId?.caseName || 'N/A'}</Typography>
                  <Typography><strong>Client Name:</strong> {viewingDocument.caseId?.clientName || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>Uploaded By</Typography>
                  <Typography>
                    {viewingDocument.uploadedBy?.firstName} {viewingDocument.uploadedBy?.lastName}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Documents;