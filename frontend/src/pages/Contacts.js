import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Chip,
  Dialog,
  Avatar,
  Menu,
  MenuItem,
  TablePagination,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Archive as ArchiveIcon,
  Restore as RestoreIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import contactService from '../services/contactService';
import ContactForm from '../components/ContactForm';

const Contacts = () => {
  const navigate = useNavigate();
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [showArchived, setShowArchived] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalContacts, setTotalContacts] = useState(0);
  const [selectedContact, setSelectedContact] = useState(null);
  const [showContactForm, setShowContactForm] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    fetchContacts();
  }, [page, rowsPerPage, searchTerm, typeFilter, showArchived]);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
        type: typeFilter,
        archived: showArchived
      };

      const data = await contactService.getContacts(params);
      setContacts(data.contacts);
      setTotalContacts(data.pagination.total);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      showSnackbar('Error fetching contacts', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleCreateContact = () => {
    setSelectedContact(null);
    setShowContactForm(true);
  };

  const handleEditContact = (contact) => {
    setSelectedContact(contact);
    setShowContactForm(true);
    setAnchorEl(null);
  };

  const handleArchiveContact = async (contact) => {
    try {
      await contactService.archiveContact(contact._id);
      showSnackbar(`${contact.fullName} archived successfully`);
      fetchContacts();
    } catch (error) {
      console.error('Error archiving contact:', error);
      showSnackbar('Error archiving contact', 'error');
    }
    setAnchorEl(null);
  };

  const handleRestoreContact = async (contact) => {
    try {
      await contactService.restoreContact(contact._id);
      showSnackbar(`${contact.fullName} restored successfully`);
      fetchContacts();
    } catch (error) {
      console.error('Error restoring contact:', error);
      showSnackbar('Error restoring contact', 'error');
    }
    setAnchorEl(null);
  };

  const handleContactSaved = (savedContact) => {
    setShowContactForm(false);
    setSelectedContact(null);
    fetchContacts();
    showSnackbar(
      selectedContact 
        ? `${savedContact.fullName} updated successfully`
        : `${savedContact.fullName} created successfully`
    );
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuClick = (event, contact) => {
    setAnchorEl(event.currentTarget);
    setSelectedContact(contact);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedContact(null);
  };

  const getContactTypeIcon = (contact) => {
    if (contact.company) {
      return <BusinessIcon color="primary" />;
    }
    return <PersonIcon color="primary" />;
  };

  const getContactDisplayName = (contact) => {
    if (contact.company) {
      return contact.company;
    }
    return contact.fullName;
  };

  const getContactSubtitle = (contact) => {
    const parts = [];
    if (contact.jobTitle) parts.push(contact.jobTitle);
    if (contact.company && contact.firstName) parts.push(contact.fullName);
    return parts.join(' • ');
  };

  const getPrimaryPhone = (contact) => {
    return contact.phones?.mobile || contact.phones?.work || contact.phones?.home || 'N/A';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Contacts {showArchived && '(Archived)'}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateContact}
        >
          Add Contact
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <TextField
            placeholder="Search contacts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
          />
          
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Type</InputLabel>
            <Select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              label="Type"
            >
              <MenuItem value="">All Types</MenuItem>
              <MenuItem value="Client">Client</MenuItem>
              <MenuItem value="Attorney">Attorney</MenuItem>
              <MenuItem value="Vendor">Vendor</MenuItem>
              <MenuItem value="Expert">Expert</MenuItem>
              <MenuItem value="Other">Other</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant={showArchived ? "contained" : "outlined"}
            onClick={() => setShowArchived(!showArchived)}
            startIcon={showArchived ? <RestoreIcon /> : <ArchiveIcon />}
          >
            {showArchived ? 'Show Active' : 'Show Archived'}
          </Button>
        </Box>
      </Paper>

      {/* Contacts Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Contact</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Cases</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : contacts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No contacts found
                </TableCell>
              </TableRow>
            ) : (
              contacts.map((contact) => (
                <TableRow 
                  key={contact._id}
                  sx={{ 
                    '&:hover': { bgcolor: 'action.hover' },
                    opacity: contact.archived ? 0.6 : 1
                  }}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {getContactTypeIcon(contact)}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {getContactDisplayName(contact)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {getContactSubtitle(contact)}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <EmailIcon fontSize="small" color="action" />
                      {contact.email || 'N/A'}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PhoneIcon fontSize="small" color="action" />
                      {getPrimaryPhone(contact)}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={contact.contactGroup || 'Other'} 
                      size="small" 
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={contact.cases?.length || 0} 
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      onClick={(e) => handleMenuClick(e, contact)}
                      size="small"
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={totalContacts}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleEditContact(selectedContact)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        {selectedContact?.archived ? (
          <MenuItem onClick={() => handleRestoreContact(selectedContact)}>
            <RestoreIcon sx={{ mr: 1 }} />
            Restore
          </MenuItem>
        ) : (
          <MenuItem onClick={() => handleArchiveContact(selectedContact)}>
            <ArchiveIcon sx={{ mr: 1 }} />
            Archive
          </MenuItem>
        )}
      </Menu>

      {/* Contact Form Dialog */}
      <Dialog
        open={showContactForm}
        onClose={() => setShowContactForm(false)}
        maxWidth="md"
        fullWidth
      >
        <ContactForm
          contact={selectedContact}
          onSave={handleContactSaved}
          onCancel={() => setShowContactForm(false)}
        />
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Contacts;