import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Grid,
  Card,
  CardContent,
  Chip,
  Dialog,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Alert,
  Menu
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  PictureAsPdf as PdfIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import invoiceService from '../services/invoiceService';
import { caseService } from '../services/caseService';
import InvoiceForm from '../components/InvoiceForm';

const Invoices = () => {
  const [invoices, setInvoices] = useState([]);
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [summary, setSummary] = useState(null);
  
  // Form dialog state
  const [formOpen, setFormOpen] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);
  
  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCase, setSelectedCase] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const statuses = ['draft', 'sent', 'paid', 'overdue', 'cancelled'];

  useEffect(() => {
    fetchInvoices();
    fetchCases();
  }, [page, rowsPerPage, selectedCase, selectedStatus, startDate, endDate]);

  useEffect(() => {
    if (searchTerm) {
      const timeoutId = setTimeout(() => {
        fetchInvoices();
      }, 500);
      return () => clearTimeout(timeoutId);
    } else {
      fetchInvoices();
    }
  }, [searchTerm]);

  const fetchInvoices = async () => {
    setLoading(true);
    setError(null);
    try {
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        caseId: selectedCase || undefined,
        status: selectedStatus || undefined,
        startDate: startDate ? startDate.toISOString() : undefined,
        endDate: endDate ? endDate.toISOString() : undefined
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      const data = await invoiceService.getInvoices(params);
      
      setInvoices(data.invoices || []);
      setTotalCount(data.pagination?.total || 0);
      setSummary(data.summary || null);
    } catch (error) {
      console.error('Error fetching invoices:', error);
      setError('Failed to load invoices');
    } finally {
      setLoading(false);
    }
  };

  const fetchCases = async () => {
    try {
      const data = await caseService.getCases({ limit: 100 });
      setCases(data.cases || []);
    } catch (error) {
      console.error('Error fetching cases:', error);
    }
  };

  const handleAdd = () => {
    setEditingInvoice(null);
    setFormOpen(true);
  };

  const handleEdit = (invoice) => {
    setEditingInvoice(invoice);
    setFormOpen(true);
    handleCloseMenu();
  };

  const handleDelete = async (invoiceId) => {
    if (window.confirm('Are you sure you want to delete this invoice?')) {
      try {
        await invoiceService.deleteInvoice(invoiceId);
        fetchInvoices();
        handleCloseMenu();
      } catch (error) {
        console.error('Error deleting invoice:', error);
        setError('Failed to delete invoice');
      }
    }
  };

  const handleGeneratePDF = async (invoice) => {
    try {
      const response = await fetch(`/api/invoices/${invoice._id}/pdf`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${invoice.invoiceNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      handleCloseMenu();
    } catch (error) {
      console.error('Error generating PDF:', error);
      setError('Failed to generate PDF');
    }
  };

  const handleStatusChange = async (invoiceId, newStatus) => {
    try {
      await invoiceService.updateInvoiceStatus(invoiceId, newStatus);
      fetchInvoices();
      handleCloseMenu();
    } catch (error) {
      console.error('Error updating invoice status:', error);
      setError('Failed to update invoice status');
    }
  };

  const handleSaveInvoice = async (invoiceData) => {
    try {
      if (editingInvoice) {
        await invoiceService.updateInvoice(editingInvoice._id, invoiceData);
      } else {
        await invoiceService.createInvoice(invoiceData);
      }
      setFormOpen(false);
      fetchInvoices();
    } catch (error) {
      console.error('Error saving invoice:', error);
      setError('Failed to save invoice');
    }
  };

  const handleOpenMenu = (event, invoice) => {
    setAnchorEl(event.currentTarget);
    setSelectedInvoice(invoice);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedInvoice(null);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCase('');
    setSelectedStatus('');
    setStartDate(null);
    setEndDate(null);
  };

  const getStatusChip = (status) => {
    const colors = {
      draft: 'default',
      sent: 'primary',
      paid: 'success',
      overdue: 'error',
      cancelled: 'secondary'
    };
    
    return (
      <Chip 
        label={status?.charAt(0).toUpperCase() + status?.slice(1)} 
        color={colors[status] || 'default'} 
        size="small" 
      />
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Invoices</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
          >
            Create Invoice
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Summary Cards */}
        {summary && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total Amount
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(summary.totalAmount)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total Paid
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {formatCurrency(summary.totalPaid)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Outstanding
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {formatCurrency(summary.totalOutstanding)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Case</InputLabel>
                <Select
                  value={selectedCase}
                  onChange={(e) => setSelectedCase(e.target.value)}
                  label="Case"
                >
                  <MenuItem value="">All Cases</MenuItem>
                  {cases.map((caseItem) => (
                    <MenuItem key={caseItem._id} value={caseItem._id}>
                      {caseItem.caseNumber} - {caseItem.caseName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  {statuses.map((status) => (
                    <MenuItem key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={setStartDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={setEndDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={1}>
              <Button
                variant="outlined"
                onClick={clearFilters}
                startIcon={<FilterIcon />}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Invoices Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Invoice #</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Case</TableCell>
                <TableCell>Client</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell align="right">Paid</TableCell>
                <TableCell align="right">Balance</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : invoices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    No invoices found
                  </TableCell>
                </TableRow>
              ) : (
                invoices.map((invoice) => (
                  <TableRow key={invoice._id}>
                    <TableCell>{invoice.invoiceNumber}</TableCell>
                    <TableCell>{formatDate(invoice.invoiceDate)}</TableCell>
                    <TableCell>
                      {invoice.caseId ? (
                        <Box>
                          <Typography variant="body2">
                            {invoice.caseId.caseNumber}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {invoice.caseId.caseName}
                          </Typography>
                        </Box>
                      ) : (
                        'N/A'
                      )}
                    </TableCell>
                    <TableCell>
                      {invoice.caseId?.clientName || 'Unknown Client'}
                    </TableCell>
                    <TableCell>{getStatusChip(invoice.status)}</TableCell>
                    <TableCell align="right">{formatCurrency(invoice.totalAmount)}</TableCell>
                    <TableCell align="right">{formatCurrency(invoice.paidAmount)}</TableCell>
                    <TableCell align="right">{formatCurrency(invoice.balanceDue)}</TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={(e) => handleOpenMenu(e, invoice)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={totalCount}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(event, newPage) => setPage(newPage)}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
          />
        </TableContainer>

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleCloseMenu}
        >
          <MenuItem onClick={() => handleEdit(selectedInvoice)}>
            <EditIcon sx={{ mr: 1 }} />
            Edit Invoice
          </MenuItem>
          <MenuItem onClick={() => handleGeneratePDF(selectedInvoice)}>
            <PdfIcon sx={{ mr: 1 }} />
            Generate PDF
          </MenuItem>
          {selectedInvoice?.status === 'draft' && (
            <MenuItem onClick={() => handleStatusChange(selectedInvoice._id, 'sent')}>
              <SendIcon sx={{ mr: 1 }} />
              Mark as Sent
            </MenuItem>
          )}
          {selectedInvoice?.status === 'sent' && (
            <MenuItem onClick={() => handleStatusChange(selectedInvoice._id, 'paid')}>
              <SendIcon sx={{ mr: 1 }} />
              Mark as Paid
            </MenuItem>
          )}
          <MenuItem 
            onClick={() => handleDelete(selectedInvoice?._id)}
            sx={{ color: 'error.main' }}
          >
            <DeleteIcon sx={{ mr: 1 }} />
            Delete Invoice
          </MenuItem>
        </Menu>

        {/* Add/Edit Dialog */}
        <Dialog
          open={formOpen}
          onClose={() => setFormOpen(false)}
          maxWidth="lg"
          fullWidth
        >
          <InvoiceForm
            invoice={editingInvoice}
            onSave={handleSaveInvoice}
            onCancel={() => setFormOpen(false)}
          />
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default Invoices;