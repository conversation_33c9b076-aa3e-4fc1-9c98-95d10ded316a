import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  Grid,
  Card,
  CardContent,
  IconButton,
  Snackbar,
  Alert,
  Tooltip
} from '@mui/material';
import { Add, Payment, Delete, Edit, Visibility } from '@mui/icons-material';
import billingService from '../services/billingService';
import { caseService } from '../services/caseService';

const Billing = () => {
  const [payments, setPayments] = useState([]);
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [editingPayment, setEditingPayment] = useState(null);
  const [viewingPayment, setViewingPayment] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [formData, setFormData] = useState({
    caseId: '',
    amount: '',
    paymentMethod: 'cash',
    description: '',
    paidBy: '',
    notes: ''
  });

  const paymentMethods = [
    { value: 'cash', label: 'Cash' },
    { value: 'check', label: 'Check' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'square', label: 'Square (Credit Card)' }
  ];

  useEffect(() => {
    loadPayments();
    loadCases();
  }, [page]);

  const loadPayments = async () => {
    try {
      const data = await billingService.getPayments({ page, limit: 10 });
      setPayments(data.payments);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('Failed to load payments:', error);
      showSnackbar('Failed to load payments', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadCases = async () => {
    try {
      const data = await caseService.getCases({ limit: 100 });
      setCases(data.cases);
    } catch (error) {
      console.error('Failed to load cases:', error);
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const paymentData = {
        ...formData,
        amount: parseFloat(formData.amount)
      };

      if (formData.paymentMethod === 'square') {
        showSnackbar('Square payment integration would be implemented here', 'info');
        return;
      }

      if (editingPayment) {
        await billingService.updatePayment(editingPayment._id, paymentData);
        showSnackbar('Payment updated successfully');
      } else {
        await billingService.recordManualPayment(paymentData);
        showSnackbar('Payment recorded successfully');
      }

      handleClose();
      loadPayments();
    } catch (error) {
      console.error('Payment error:', error);
      showSnackbar(error.response?.data?.message || 'Payment failed', 'error');
    }
  };

  const handleEdit = (payment) => {
    setEditingPayment(payment);
    setFormData({
      caseId: payment.caseId._id,
      amount: payment.amount.toString(),
      paymentMethod: payment.paymentMethod,
      description: payment.description,
      paidBy: payment.paidBy,
      notes: payment.notes || ''
    });
    setOpen(true);
  };

  const handleView = async (payment) => {
    try {
      const fullPayment = await billingService.getPayment(payment._id);
      setViewingPayment(fullPayment);
      setShowDetails(true);
    } catch (error) {
      console.error('Failed to load payment details:', error);
      showSnackbar('Failed to load payment details', 'error');
    }
  };

  const handleDelete = async (payment) => {
    if (window.confirm('Are you sure you want to delete this payment?')) {
      try {
        await billingService.deletePayment(payment._id);
        showSnackbar('Payment deleted successfully');
        loadPayments();
      } catch (error) {
        console.error('Delete error:', error);
        showSnackbar('Failed to delete payment', 'error');
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    setEditingPayment(null);
    setFormData({
      caseId: '',
      amount: '',
      paymentMethod: 'cash',
      description: '',
      paidBy: '',
      notes: ''
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getPaymentMethodLabel = (method) => {
    const methodObj = paymentMethods.find(m => m.value === method);
    return methodObj ? methodObj.label : method;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading payments...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Billing & Payments</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpen(true)}
        >
          Record Payment
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Payments
              </Typography>
              <Typography variant="h4">
                {formatCurrency(payments.reduce((sum, p) => sum + p.amount, 0))}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                This Month
              </Typography>
              <Typography variant="h4">
                {formatCurrency(
                  payments
                    .filter(p => {
                      const paymentDate = new Date(p.createdAt);
                      const now = new Date();
                      return paymentDate.getMonth() === now.getMonth() && 
                             paymentDate.getFullYear() === now.getFullYear();
                    })
                    .reduce((sum, p) => sum + p.amount, 0)
                )}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Records
              </Typography>
              <Typography variant="h4">
                {payments.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Payments Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Case</TableCell>
                <TableCell>Client</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Method</TableCell>
                <TableCell>Paid By</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment._id}>
                  <TableCell>
                    {new Date(payment.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {payment.caseId?.caseNumber || 'N/A'}
                  </TableCell>
                  <TableCell>
                    {payment.caseId?.clientName || 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {formatCurrency(payment.amount)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {getPaymentMethodLabel(payment.paymentMethod)}
                  </TableCell>
                  <TableCell>{payment.paidBy}</TableCell>
                  <TableCell>
                    <Chip
                      label={payment.status}
                      color={getStatusColor(payment.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleView(payment)}
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Payment">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(payment)}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Payment">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDelete(payment)}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Pagination */}
      <Box display="flex" justifyContent="center" mt={3}>
        <Pagination
          count={totalPages}
          page={page}
          onChange={(e, value) => setPage(value)}
          color="primary"
        />
      </Box>

      {/* Payment Form Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingPayment ? 'Edit Payment' : 'Record New Payment'}
        </DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Case</InputLabel>
                  <Select
                    value={formData.caseId}
                    onChange={(e) => setFormData({ ...formData, caseId: e.target.value })}
                    label="Case"
                  >
                    {cases.map((caseItem) => (
                      <MenuItem key={caseItem._id} value={caseItem._id}>
                        {caseItem.caseNumber} - {caseItem.clientName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                  required
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Payment Method</InputLabel>
                  <Select
                    value={formData.paymentMethod}
                    onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value })}
                    label="Payment Method"
                  >
                    {paymentMethods.map((method) => (
                      <MenuItem key={method.value} value={method.value}>
                        {method.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Paid By"
                  value={formData.paidBy}
                  onChange={(e) => setFormData({ ...formData, paidBy: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  required
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes (Optional)"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button type="submit" variant="contained">
              {editingPayment ? 'Update Payment' : 'Record Payment'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Payment Details Dialog */}
      <Dialog
        open={showDetails}
        onClose={() => setShowDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Payment Details</Typography>
            <IconButton onClick={() => setShowDetails(false)}>
              <Visibility />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {viewingPayment && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>Payment Information</Typography>
                  <Typography><strong>Amount:</strong> {formatCurrency(viewingPayment.amount)}</Typography>
                  <Typography><strong>Method:</strong> {getPaymentMethodLabel(viewingPayment.paymentMethod)}</Typography>
                  <Typography><strong>Status:</strong> {viewingPayment.status}</Typography>
                  <Typography><strong>Paid By:</strong> {viewingPayment.paidBy}</Typography>
                  <Typography><strong>Date:</strong> {new Date(viewingPayment.createdAt).toLocaleDateString()}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>Case Information</Typography>
                  <Typography><strong>Case Number:</strong> {viewingPayment.caseId?.caseNumber || 'N/A'}</Typography>
                  <Typography><strong>Client Name:</strong> {viewingPayment.caseId?.clientName || 'N/A'}</Typography>
                  <Typography><strong>Description:</strong> {viewingPayment.description}</Typography>
                  {viewingPayment.notes && (
                    <Typography><strong>Notes:</strong> {viewingPayment.notes}</Typography>
                  )}
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>Processed By</Typography>
                  <Typography>
                    {viewingPayment.processedBy?.firstName} {viewingPayment.processedBy?.lastName}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Billing;