import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuth } from './contexts/AuthContext';
import Navbar from './components/Navbar';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Leads from './pages/Leads';
import Cases from './pages/Cases';
import CaseDetail from './pages/CaseDetail';
import Contacts from './pages/Contacts';
import TimeEntries from './pages/TimeEntries';
import Expenses from './pages/Expenses';
import Invoices from './pages/Invoices';
import Notes from './pages/Notes';
import Tasks from './pages/Tasks';
import Documents from './pages/Documents';
import Billing from './pages/Billing';
import Employees from './pages/Employees';
import Reports from './pages/Reports';
import Calendar from './pages/Calendar';

function App() {
  const { user, loading } = useAuth();

  if (loading) {
    return <Box>Loading...</Box>;
  }

  if (!user) {
    return <Login />;
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Navbar />
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/leads" element={<Leads />} />
          <Route path="/cases" element={<Cases />} />
          <Route path="/cases/:id" element={<CaseDetail />} />
          <Route path="/contacts" element={<Contacts />} />
          <Route path="/time-entries" element={<TimeEntries />} />
          <Route path="/expenses" element={<Expenses />} />
          <Route path="/invoices" element={<Invoices />} />
          <Route path="/notes" element={<Notes />} />
          <Route path="/tasks" element={<Tasks />} />
          <Route path="/documents" element={<Documents />} />
          <Route path="/billing" element={<Billing />} />
          <Route path="/calendar" element={<Calendar />} />
          {user.role === 'admin' && (
            <Route path="/employees" element={<Employees />} />
          )}
          <Route path="/reports" element={<Reports />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Box>
    </Box>
  );
}

export default App;